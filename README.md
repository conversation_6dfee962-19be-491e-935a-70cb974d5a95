# CodeMind AI Assistant

An intelligent AI-powered coding companion for VS Code that brings the capabilities of advanced AI models directly into your development environment. This extension provides intelligent code assistance, explanations, generation, and refactoring capabilities with deep understanding of your codebase.

## Features

### 🤖 AI-Powered Chat Interface
- Interactive chat panel integrated into VS Code
- Conversation history with context awareness
- Support for both OpenAI and Anthropic models
- Markdown rendering for formatted responses

### 💻 Code Intelligence
- **Code Explanation**: Get detailed explanations of selected code
- **Code Generation**: Generate code from natural language descriptions
- **Code Refactoring**: Improve code quality with AI suggestions
- **Context Awareness**: AI understands your workspace and current files

### 🛠️ Development Tools
- **File Operations**: Create, read, and modify files through AI
- **Terminal Integration**: Execute commands via AI assistance
- **Workspace Analysis**: AI understands your project structure
- **Code Insertion**: Direct code insertion at cursor or replace selection

### ⚙️ Flexible Configuration
- Support for multiple AI providers (OpenAI, Anthropic)
- Customizable model parameters
- Secure API key management
- Temperature and token limit controls

## Installation

### From Source
1. Clone this repository
2. Install dependencies: `npm install`
3. Compile TypeScript: `npm run compile`
4. Press F5 to launch a new VS Code window with the extension loaded

### Configuration
1. Open VS Code settings (Cmd/Ctrl + ,)
2. Search for "Augment AI"
3. Configure your preferred AI provider and API key:
   - **API Provider**: Choose between OpenAI or Anthropic
   - **API Key**: Enter your API key for the selected provider
   - **Model**: Specify the model to use (e.g., `claude-3-sonnet-20240229` for Anthropic)
   - **Max Tokens**: Set the maximum response length
   - **Temperature**: Control response creativity (0-2)

## Usage

### Opening the Chat Interface
- Use Command Palette: `CodeMind AI: Open Chat`
- Or find the "CodeMind AI Chat" panel in the Explorer sidebar

### Code Operations
1. **Explain Code**: Select code and use `CodeMind AI: Explain Selected Code`
2. **Generate Code**: Use `CodeMind AI: Generate Code` and describe what you need
3. **Refactor Code**: Select code and use `CodeMind AI: Refactor Code`

### Chat Commands
In the chat interface, you can:
- Ask questions about your code
- Request code generation
- Get explanations and documentation
- Ask for debugging help
- Request refactoring suggestions

### Code Actions
When the AI provides code in responses:
- **Copy**: Copy code to clipboard
- **Insert**: Insert code at current cursor position

## API Providers

### OpenAI
- Models: GPT-4, GPT-3.5-turbo, etc.
- Get API key from: https://platform.openai.com/api-keys

### Anthropic
- Models: Claude-3 Sonnet, Claude-3 Haiku, etc.
- Get API key from: https://console.anthropic.com/

## Commands

| Command | Description |
|---------|-------------|
| `codemind-ai.openChat` | Open the AI chat interface |
| `codemind-ai.explainCode` | Explain selected code |
| `codemind-ai.generateCode` | Generate code from description |
| `codemind-ai.refactorCode` | Refactor selected code |

## Settings

| Setting | Description | Default |
|---------|-------------|---------|
| `codemind-ai.apiProvider` | AI API provider | `anthropic` |
| `codemind-ai.apiKey` | API key for the provider | `""` |
| `codemind-ai.model` | AI model to use | `claude-3-sonnet-20240229` |
| `codemind-ai.maxTokens` | Maximum tokens for responses | `4000` |
| `codemind-ai.temperature` | Response creativity (0-2) | `0.1` |

## Development

### Project Structure
```
src/
├── extension.ts              # Main extension entry point
├── managers/
│   ├── ConfigurationManager.ts    # Settings and API configuration
│   ├── CodeContextManager.ts      # Code analysis and context
│   └── FileOperationsManager.ts   # File system operations
├── providers/
│   ├── AugmentAIProvider.ts       # AI API integration
│   └── ChatWebviewProvider.ts     # Chat interface
└── tools/
    └── ToolManager.ts             # Tool execution system
```

### Building
```bash
npm install          # Install dependencies
npm run compile      # Compile TypeScript
npm run watch        # Watch for changes
npm run lint         # Run linter
npm run test         # Run tests
```

### Testing
```bash
npm test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Security

- API keys are stored securely in VS Code's configuration
- No data is logged or transmitted except to your chosen AI provider
- All communication uses HTTPS

## License

MIT License - see LICENSE file for details

## Troubleshooting

### Common Issues

**"API key is required"**
- Configure your API key in VS Code settings under "Augment AI"

**"Rate limit exceeded"**
- You've hit your API provider's rate limit. Wait and try again.

**"No active editor found"**
- Make sure you have a file open when using code-specific commands

**Extension not loading**
- Check the VS Code Developer Console for errors
- Ensure all dependencies are installed with `npm install`

### Getting Help

- Check the VS Code Output panel for error messages
- Review the extension's logs in the Developer Console
- File issues on the GitHub repository

## Roadmap

- [ ] Advanced code analysis and suggestions
- [ ] Integration with version control systems
- [ ] Custom tool creation and management
- [ ] Multi-file refactoring capabilities
- [ ] Code review and quality analysis
- [ ] Integration with testing frameworks
- [ ] Custom AI model fine-tuning support

---

**Built with ❤️ to bring AI assistance directly into your coding workflow**
