# Test file for CodeMind AI Extension
def hello_world():
    print("Hello, CodeMind AI!")
    return "Welcome to AI-powered coding!"

# Select this code and try right-click -> "Explain Selected Code"
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

if __name__ == "__main__":
    hello_world()
    numbers = [64, 34, 25, 12, 22, 11, 90]
    sorted_numbers = bubble_sort(numbers)
    print(f"Sorted array: {sorted_numbers}")
