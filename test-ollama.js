#!/usr/bin/env node

/**
 * Test script to verify Ollama integration with CodeMind AI
 * Run this to test if <PERSON>lla<PERSON> is working before using the VS Code extension
 */

const axios = require('axios');

const OLLAMA_ENDPOINT = 'http://localhost:11434';
const MODEL = 'deepseek-coder:6.7b';

async function testOllamaConnection() {
    console.log('🔍 Testing Ollama connection...');
    
    try {
        // Test if Ollama is running
        const response = await axios.get(`${OLLAMA_ENDPOINT}/api/tags`);
        console.log('✅ Ollama is running!');
        
        // Check if our model is available
        const models = response.data.models || [];
        const hasModel = models.some(model => model.name.includes('deepseek-coder'));
        
        if (hasModel) {
            console.log('✅ DeepSeek Coder model is available!');
            return true;
        } else {
            console.log('❌ DeepSeek Coder model not found.');
            console.log('📥 Available models:', models.map(m => m.name));
            console.log('💡 Run: ollama pull deepseek-coder:6.7b');
            return false;
        }
    } catch (error) {
        console.log('❌ Ollama connection failed:', error.message);
        console.log('💡 Make sure Ollama is running: ollama serve');
        return false;
    }
}

async function testCodeGeneration() {
    console.log('\n🧪 Testing code generation...');
    
    const prompt = `System: You are a helpful coding assistant.

User: Write a simple Python function to calculate the factorial of a number.`;

    try {
        const response = await axios.post(`${OLLAMA_ENDPOINT}/api/generate`, {
            model: MODEL,
            prompt: prompt,
            stream: false,
            options: {
                temperature: 0.1,
                num_predict: 500
            }
        });

        if (response.data && response.data.response) {
            console.log('✅ Code generation successful!');
            console.log('🤖 AI Response:');
            console.log('─'.repeat(50));
            console.log(response.data.response);
            console.log('─'.repeat(50));
            return true;
        } else {
            console.log('❌ No response from model');
            return false;
        }
    } catch (error) {
        console.log('❌ Code generation failed:', error.message);
        return false;
    }
}

async function main() {
    console.log('🚀 CodeMind AI - Ollama Integration Test\n');

    const connectionOk = await testOllamaConnection();
    if (!connectionOk) {
        process.exit(1);
    }

    const generationOk = await testCodeGeneration();
    if (!generationOk) {
        process.exit(1);
    }

    console.log('\n🎉 All tests passed! CodeMind AI is ready to use with Ollama.');
    console.log('💡 Now install the VS Code extension and start coding!');
}

main().catch(console.error);
