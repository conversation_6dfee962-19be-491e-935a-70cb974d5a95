{"version": 3, "file": "workerpool.js", "sources": ["../src/environment.js", "../src/Promise.js", "../src/validateOptions.js", "../src/generated/embeddedWorker.js", "../src/WorkerHandler.js", "../src/debug-port-allocator.js", "../src/Pool.js", "../src/transfer.js", "../src/worker.js", "../src/index.js"], "sourcesContent": ["\n// source: https://github.com/flexdinesh/browser-or-node\n// source: https://github.com/mozilla/pdf.js/blob/7ea0e40e588864cd938d1836ec61f1928d3877d3/src/shared/util.js#L24\nvar isNode = function (nodeProcess) {\n  return (\n    typeof nodeProcess !== 'undefined' &&\n    nodeProcess.versions != null &&\n    nodeProcess.versions.node != null &&\n    nodeProcess + '' === '[object process]'\n  );\n}\nmodule.exports.isNode = isNode\n\n// determines the JavaScript platform: browser or node\nmodule.exports.platform = typeof process !== 'undefined' && isNode(process)\n  ? 'node'\n  : 'browser';\n\n// determines whether the code is running in main thread or not\n// note that in node.js we have to check both worker_thread and child_process\nvar worker_threads = module.exports.platform === 'node' && require('worker_threads');\nmodule.exports.isMainThread = module.exports.platform === 'node'\n  ? ((!worker_threads || worker_threads.isMainThread) && !process.connected)\n  : typeof Window !== 'undefined';\n\n// determines the number of cpus available\nmodule.exports.cpus = module.exports.platform === 'browser'\n  ? self.navigator.hardwareConcurrency\n  : require('os').cpus().length;\n\n", "'use strict';\n\n/**\n * Promise\n *\n * Inspired by https://gist.github.com/RubaXa/8501359 from RubaXa <<EMAIL>>\n * @template T\n * @template [E=Error]\n * @param {Function} handler   Called as handler(resolve: Function, reject: Function)\n * @param {Promise} [parent]   Parent promise for propagation of cancel and timeout\n */\nfunction Promise(handler, parent) {\n  var me = this;\n\n  if (!(this instanceof Promise)) {\n    throw new SyntaxError('Constructor must be called with the new operator');\n  }\n\n  if (typeof handler !== 'function') {\n    throw new SyntaxError('Function parameter handler(resolve, reject) missing');\n  }\n\n  var _onSuccess = [];\n  var _onFail = [];\n\n  // status\n  /**\n   * @readonly\n   */\n  this.resolved = false;\n  /**\n   * @readonly\n   */\n  this.rejected = false;\n  /**\n   * @readonly\n   */\n  this.pending = true;\n  /**\n   * @readonly\n   */\n  this[Symbol.toStringTag] = 'Promise';\n\n  /**\n   * Process onSuccess and onFail callbacks: add them to the queue.\n   * Once the promise is resolved, the function _promise is replace.\n   * @param {Function} onSuccess\n   * @param {Function} onFail\n   * @private\n   */\n  var _process = function (onSuccess, onFail) {\n    _onSuccess.push(onSuccess);\n    _onFail.push(onFail);\n  };\n\n  /**\n   * Add an onSuccess callback and optionally an onFail callback to the Promise\n   * @template TT\n   * @template [TE=never]\n   * @param {(r: T) => TT | PromiseLike<TT>} onSuccess\n   * @param {(r: E) => TE | PromiseLike<TE>} [onFail]\n   * @returns {Promise<TT | TE, any>} promise\n   */\n  this.then = function (onSuccess, onFail) {\n    return new Promise(function (resolve, reject) {\n      var s = onSuccess ? _then(onSuccess, resolve, reject) : resolve;\n      var f = onFail    ? _then(onFail,    resolve, reject) : reject;\n\n      _process(s, f);\n    }, me);\n  };\n\n  /**\n   * Resolve the promise\n   * @param {*} result\n   * @type {Function}\n   */\n  var _resolve = function (result) {\n    // update status\n    me.resolved = true;\n    me.rejected = false;\n    me.pending = false;\n\n    _onSuccess.forEach(function (fn) {\n      fn(result);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onSuccess(result);\n    };\n\n    _resolve = _reject = function () { };\n\n    return me;\n  };\n\n  /**\n   * Reject the promise\n   * @param {Error} error\n   * @type {Function}\n   */\n  var _reject = function (error) {\n    // update status\n    me.resolved = false;\n    me.rejected = true;\n    me.pending = false;\n\n    _onFail.forEach(function (fn) {\n      fn(error);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onFail(error);\n    };\n\n    _resolve = _reject = function () { }\n\n    return me;\n  };\n\n  /**\n   * Cancel the promise. This will reject the promise with a CancellationError\n   * @returns {this} self\n   */\n  this.cancel = function () {\n    if (parent) {\n      parent.cancel();\n    }\n    else {\n      _reject(new CancellationError());\n    }\n\n    return me;\n  };\n\n  /**\n   * Set a timeout for the promise. If the promise is not resolved within\n   * the time, the promise will be cancelled and a TimeoutError is thrown.\n   * If the promise is resolved in time, the timeout is removed.\n   * @param {number} delay     Delay in milliseconds\n   * @returns {this} self\n   */\n  this.timeout = function (delay) {\n    if (parent) {\n      parent.timeout(delay);\n    }\n    else {\n      var timer = setTimeout(function () {\n        _reject(new TimeoutError('Promise timed out after ' + delay + ' ms'));\n      }, delay);\n\n      me.always(function () {\n        clearTimeout(timer);\n      });\n    }\n\n    return me;\n  };\n\n  // attach handler passing the resolve and reject functions\n  handler(function (result) {\n    _resolve(result);\n  }, function (error) {\n    _reject(error);\n  });\n}\n\n/**\n * Execute given callback, then call resolve/reject based on the returned result\n * @param {Function} callback\n * @param {Function} resolve\n * @param {Function} reject\n * @returns {Function}\n * @private\n */\nfunction _then(callback, resolve, reject) {\n  return function (result) {\n    try {\n      var res = callback(result);\n      if (res && typeof res.then === 'function' && typeof res['catch'] === 'function') {\n        // method returned a promise\n        res.then(resolve, reject);\n      }\n      else {\n        resolve(res);\n      }\n    }\n    catch (error) {\n      reject(error);\n    }\n  }\n}\n\n/**\n * Add an onFail callback to the Promise\n * @template TT\n * @param {(error: E) => TT | PromiseLike<TT>} onFail\n * @returns {Promise<T | TT>} promise\n */\nPromise.prototype['catch'] = function (onFail) {\n  return this.then(null, onFail);\n};\n\n// TODO: add support for Promise.catch(Error, callback)\n// TODO: add support for Promise.catch(Error, Error, callback)\n\n/**\n * Execute given callback when the promise either resolves or rejects.\n * @template TT\n * @param {() => Promise<TT>} fn\n * @returns {Promise<TT>} promise\n */\nPromise.prototype.always = function (fn) {\n  return this.then(fn, fn);\n};\n\n/**\n  * Execute given callback when the promise either resolves or rejects.\n  * Same semantics as Node's Promise.finally()\n  * @param {Function | null | undefined} [fn]\n  * @returns {Promise} promise\n  */\nPromise.prototype.finally = function (fn) {\n  const me = this;\n\n  const final = function() {\n    return new Promise((resolve) => resolve())\n      .then(fn)\n      .then(() => me);\n  };\n\n  return this.then(final, final);\n}\n\n/**\n * Create a promise which resolves when all provided promises are resolved,\n * and fails when any of the promises resolves.\n * @param {Promise[]} promises\n * @returns {Promise<any[], any>} promise\n */\nPromise.all = function (promises){\n  return new Promise(function (resolve, reject) {\n    var remaining = promises.length,\n        results = [];\n\n    if (remaining) {\n      promises.forEach(function (p, i) {\n        p.then(function (result) {\n          results[i] = result;\n          remaining--;\n          if (remaining == 0) {\n            resolve(results);\n          }\n        }, function (error) {\n          remaining = 0;\n          reject(error);\n        });\n      });\n    }\n    else {\n      resolve(results);\n    }\n  });\n};\n\n/**\n * Create a promise resolver\n * @returns {{promise: Promise, resolve: Function, reject: Function}} resolver\n */\nPromise.defer = function () {\n  var resolver = {};\n\n  resolver.promise = new Promise(function (resolve, reject) {\n    resolver.resolve = resolve;\n    resolver.reject = reject;\n  });\n\n  return resolver;\n};\n\n/**\n * Create a cancellation error\n * @param {String} [message]\n * @extends Error\n */\nfunction CancellationError(message) {\n  this.message = message || 'promise cancelled';\n  this.stack = (new Error()).stack;\n}\n\nCancellationError.prototype = new Error();\nCancellationError.prototype.constructor = Error;\nCancellationError.prototype.name = 'CancellationError';\n\nPromise.CancellationError = CancellationError;\n\n\n/**\n * Create a timeout error\n * @param {String} [message]\n * @extends Error\n */\nfunction TimeoutError(message) {\n  this.message = message || 'timeout exceeded';\n  this.stack = (new Error()).stack;\n}\n\nTimeoutError.prototype = new Error();\nTimeoutError.prototype.constructor = Error;\nTimeoutError.prototype.name = 'TimeoutError';\n\nPromise.TimeoutError = TimeoutError;\n\n\nexports.Promise = Promise;\n", "/**\n * Validate that the object only contains known option names\n * - Throws an error when unknown options are detected\n * - Throws an error when some of the allowed options are attached\n * @param {Object | undefined} options\n * @param {string[]} allowedOptionNames\n * @param {string} objectName\n * @retrun {Object} Returns the original options\n */\nexports.validateOptions = function validateOptions(options, allowedOptionNames, objectName) {\n  if (!options) {\n    return\n  }\n\n  var optionNames = options ?  Object.keys(options) : []\n\n  // check for unknown properties\n  var unknownOptionName = optionNames.find(optionName => !allowedOptionNames.includes(optionName))\n  if (unknownOptionName) {\n    throw new Error('Object \"' + objectName + '\" contains an unknown option \"' + unknownOptionName + '\"')\n  }\n\n  // check for inherited properties which are not present on the object itself\n  var illegalOptionName = allowedOptionNames.find(allowedOptionName => {\n    return Object.prototype[allowedOptionName] && !optionNames.includes(allowedOptionName)\n  })\n  if (illegalOptionName) {\n    throw new Error('Object \"' + objectName + '\" contains an inherited option \"' + illegalOptionName + '\" which is ' +\n      'not defined in the object itself but in its prototype. Only plain objects are allowed. ' +\n      'Please remove the option from the prototype or override it with a value \"undefined\".')\n  }\n\n  return options\n}\n\n// source: https://developer.mozilla.org/en-US/docs/Web/API/Worker/Worker\nexports.workerOptsNames = [\n  'credentials', 'name', 'type' ]\n\n// source: https://nodejs.org/api/child_process.html#child_processforkmodulepath-args-options\nexports.forkOptsNames = [\n  'cwd', 'detached', 'env', 'execPath', 'execArgv', 'gid', 'serialization',\n  'signal', 'killSignal', 'silent', 'stdio', 'uid', 'windowsVerbatimArguments',\n  'timeout'\n]\n\n// source: https://nodejs.org/api/worker_threads.html#new-workerfilename-options\nexports.workerThreadOptsNames = [\n  'argv', 'env', 'eval', 'execArgv', 'stdin', 'stdout', 'stderr', 'workerData',\n  'trackUnmanagedFds', 'transferList', 'resourceLimits', 'name'\n]\n", "/**\n * embeddedWorker.js contains an embedded version of worker.js.\n * This file is automatically generated,\n * changes made in this file will be overwritten.\n */\nmodule.exports = \"!function(e,n){\\\"object\\\"==typeof exports&&\\\"undefined\\\"!=typeof module?module.exports=n():\\\"function\\\"==typeof define&&define.amd?define(n):(e=\\\"undefined\\\"!=typeof globalThis?globalThis:e||self).worker=n()}(this,(function(){\\\"use strict\\\";function e(n){return e=\\\"function\\\"==typeof Symbol&&\\\"symbol\\\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\\\"function\\\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\\\"symbol\\\":typeof e},e(n)}function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,\\\"default\\\")?e.default:e}var t={};var r=function(e,n){this.message=e,this.transfer=n},o={};function i(e,n){var t=this;if(!(this instanceof i))throw new SyntaxError(\\\"Constructor must be called with the new operator\\\");if(\\\"function\\\"!=typeof e)throw new SyntaxError(\\\"Function parameter handler(resolve, reject) missing\\\");var r=[],o=[];this.resolved=!1,this.rejected=!1,this.pending=!0,this[Symbol.toStringTag]=\\\"Promise\\\";var a=function(e,n){r.push(e),o.push(n)};this.then=function(e,n){return new i((function(t,r){var o=e?u(e,t,r):t,i=n?u(n,t,r):r;a(o,i)}),t)};var f=function(e){return t.resolved=!0,t.rejected=!1,t.pending=!1,r.forEach((function(n){n(e)})),a=function(n,t){n(e)},f=d=function(){},t},d=function(e){return t.resolved=!1,t.rejected=!0,t.pending=!1,o.forEach((function(n){n(e)})),a=function(n,t){t(e)},f=d=function(){},t};this.cancel=function(){return n?n.cancel():d(new s),t},this.timeout=function(e){if(n)n.timeout(e);else{var r=setTimeout((function(){d(new c(\\\"Promise timed out after \\\"+e+\\\" ms\\\"))}),e);t.always((function(){clearTimeout(r)}))}return t},e((function(e){f(e)}),(function(e){d(e)}))}function u(e,n,t){return function(r){try{var o=e(r);o&&\\\"function\\\"==typeof o.then&&\\\"function\\\"==typeof o.catch?o.then(n,t):n(o)}catch(e){t(e)}}}function s(e){this.message=e||\\\"promise cancelled\\\",this.stack=(new Error).stack}function c(e){this.message=e||\\\"timeout exceeded\\\",this.stack=(new Error).stack}return i.prototype.catch=function(e){return this.then(null,e)},i.prototype.always=function(e){return this.then(e,e)},i.prototype.finally=function(e){var n=this,t=function(){return new i((function(e){return e()})).then(e).then((function(){return n}))};return this.then(t,t)},i.all=function(e){return new i((function(n,t){var r=e.length,o=[];r?e.forEach((function(e,i){e.then((function(e){o[i]=e,0==--r&&n(o)}),(function(e){r=0,t(e)}))})):n(o)}))},i.defer=function(){var e={};return e.promise=new i((function(n,t){e.resolve=n,e.reject=t})),e},s.prototype=new Error,s.prototype.constructor=Error,s.prototype.name=\\\"CancellationError\\\",i.CancellationError=s,c.prototype=new Error,c.prototype.constructor=Error,c.prototype.name=\\\"TimeoutError\\\",i.TimeoutError=c,o.Promise=i,function(n){var t=r,i=o.Promise,u=\\\"__workerpool-cleanup__\\\",s={exit:function(){}},c={addAbortListener:function(e){s.abortListeners.push(e)},emit:s.emit};if(\\\"undefined\\\"!=typeof self&&\\\"function\\\"==typeof postMessage&&\\\"function\\\"==typeof addEventListener)s.on=function(e,n){addEventListener(e,(function(e){n(e.data)}))},s.send=function(e,n){n?postMessage(e,n):postMessage(e)};else{if(\\\"undefined\\\"==typeof process)throw new Error(\\\"Script must be executed as a worker\\\");var a;try{a=require(\\\"worker_threads\\\")}catch(n){if(\\\"object\\\"!==e(n)||null===n||\\\"MODULE_NOT_FOUND\\\"!==n.code)throw n}if(a&&null!==a.parentPort){var f=a.parentPort;s.send=f.postMessage.bind(f),s.on=f.on.bind(f),s.exit=process.exit.bind(process)}else s.on=process.on.bind(process),s.send=function(e){process.send(e)},s.on(\\\"disconnect\\\",(function(){process.exit(1)})),s.exit=process.exit.bind(process)}function d(e){return Object.getOwnPropertyNames(e).reduce((function(n,t){return Object.defineProperty(n,t,{value:e[t],enumerable:!0})}),{})}function l(e){return e&&\\\"function\\\"==typeof e.then&&\\\"function\\\"==typeof e.catch}s.methods={},s.methods.run=function(e,n){var t=new Function(\\\"return (\\\"+e+\\\").apply(this, arguments);\\\");return t.worker=c,t.apply(t,n)},s.methods.methods=function(){return Object.keys(s.methods)},s.terminationHandler=void 0,s.abortListenerTimeout=1e3,s.abortListeners=[],s.terminateAndExit=function(e){var n=function(){s.exit(e)};if(!s.terminationHandler)return n();var t=s.terminationHandler(e);return l(t)?(t.then(n,n),t):(n(),new i((function(e,n){n(new Error(\\\"Worker terminating\\\"))})))},s.cleanup=function(e){if(!s.abortListeners.length)return s.send({id:e,method:u,error:d(new Error(\\\"Worker terminating\\\"))}),new i((function(e){e()}));var n,t=s.abortListeners.map((function(e){return e()})),r=new i((function(e,t){n=setTimeout((function(){t(new Error(\\\"Timeout occured waiting for abort handler, killing worker\\\"))}),s.abortListenerTimeout)})),o=i.all(t).then((function(){clearTimeout(n),s.abortListeners.length||(s.abortListeners=[])}),(function(){clearTimeout(n),s.exit()}));return i.all([o,r]).then((function(){s.send({id:e,method:u,error:null})}),(function(n){s.send({id:e,method:u,error:n?d(n):null})}))};var p=null;s.on(\\\"message\\\",(function(e){if(\\\"__workerpool-terminate__\\\"===e)return s.terminateAndExit(0);if(e.method===u)return s.cleanup(e.id);try{var n=s.methods[e.method];if(!n)throw new Error('Unknown method \\\"'+e.method+'\\\"');p=e.id;var r=n.apply(n,e.params);l(r)?r.then((function(n){n instanceof t?s.send({id:e.id,result:n.message,error:null},n.transfer):s.send({id:e.id,result:n,error:null}),p=null})).catch((function(n){s.send({id:e.id,result:null,error:d(n)}),p=null})):(r instanceof t?s.send({id:e.id,result:r.message,error:null},r.transfer):s.send({id:e.id,result:r,error:null}),p=null)}catch(n){s.send({id:e.id,result:null,error:d(n)})}})),s.register=function(e,n){if(e)for(var t in e)e.hasOwnProperty(t)&&(s.methods[t]=e[t],s.methods[t].worker=c);n&&(s.terminationHandler=n.onTerminate,s.abortListenerTimeout=n.abortListenerTimeout||1e3),s.send(\\\"ready\\\")},s.emit=function(e){if(p){if(e instanceof t)return void s.send({id:p,isEvent:!0,payload:e.message},e.transfer);s.send({id:p,isEvent:!0,payload:e})}},n.add=s.register,n.emit=s.emit}(t),n(t)}));\\n//# sourceMappingURL=worker.min.js.map\\n\";\n", "'use strict';\n\nvar {Promise} = require('./Promise');\nvar environment = require('./environment');\nconst {validateOptions, forkOptsNames, workerThreadOptsNames, workerOptsNames} = require(\"./validateOptions\");\n\n/**\n * Special message sent by parent which causes a child process worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n/**\n * Special message by parent which causes a child process worker to perform cleaup\n * steps before determining if the child process worker should be terminated.\n */\nvar CLEANUP_METHOD_ID = '__workerpool-cleanup__';\n\nfunction ensureWorkerThreads() {\n  var WorkerThreads = tryRequireWorkerThreads()\n  if (!WorkerThreads) {\n    throw new Error('WorkerPool: workerType = \\'thread\\' is not supported, Node >= 11.7.0 required')\n  }\n\n  return WorkerThreads;\n}\n\n// check whether Worker is supported by the browser\nfunction ensureWebWorker() {\n  // Workaround for a bug in PhantomJS (Or QtWebkit): https://github.com/ariya/phantomjs/issues/14534\n  if (typeof Worker !== 'function' && (typeof Worker !== 'object' || typeof Worker.prototype.constructor !== 'function')) {\n    throw new Error('WorkerPool: Web Workers not supported');\n  }\n}\n\nfunction tryRequireWorkerThreads() {\n  try {\n    return require('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads available (old version of node.js)\n      return null;\n    } else {\n      throw error;\n    }\n  }\n}\n\n// get the default worker script\nfunction getDefaultWorker() {\n  if (environment.platform === 'browser') {\n    // test whether the browser supports all features that we need\n    if (typeof Blob === 'undefined') {\n      throw new Error('Blob not supported by the browser');\n    }\n    if (!window.URL || typeof window.URL.createObjectURL !== 'function') {\n      throw new Error('URL.createObjectURL not supported by the browser');\n    }\n\n    // use embedded worker.js\n    var blob = new Blob([require('./generated/embeddedWorker')], {type: 'text/javascript'});\n    return window.URL.createObjectURL(blob);\n  }\n  else {\n    // use external worker.js in current directory\n    return __dirname + '/worker.js';\n  }\n}\n\nfunction setupWorker(script, options) {\n  if (options.workerType === 'web') { // browser only\n    ensureWebWorker();\n    return setupBrowserWorker(script, options.workerOpts, Worker);\n  } else if (options.workerType === 'thread') { // node.js only\n    WorkerThreads = ensureWorkerThreads();\n    return setupWorkerThreadWorker(script, WorkerThreads, options);\n  } else if (options.workerType === 'process' || !options.workerType) { // node.js only\n    return setupProcessWorker(script, resolveForkOptions(options), require('child_process'));\n  } else { // options.workerType === 'auto' or undefined\n    if (environment.platform === 'browser') {\n      ensureWebWorker();\n      return setupBrowserWorker(script, options.workerOpts, Worker);\n    }\n    else { // environment.platform === 'node'\n      var WorkerThreads = tryRequireWorkerThreads();\n      if (WorkerThreads) {\n        return setupWorkerThreadWorker(script, WorkerThreads, options);\n      } else {\n        return setupProcessWorker(script, resolveForkOptions(options), require('child_process'));\n      }\n    }\n  }\n}\n\nfunction setupBrowserWorker(script, workerOpts, Worker) {\n  // validate the options right before creating the worker (not when creating the pool)\n  validateOptions(workerOpts, workerOptsNames, 'workerOpts')\n\n  // create the web worker\n  var worker = new Worker(script, workerOpts);\n\n  worker.isBrowserWorker = true;\n  // add node.js API to the web worker\n  worker.on = function (event, callback) {\n    this.addEventListener(event, function (message) {\n      callback(message.data);\n    });\n  };\n  worker.send = function (message, transfer) {\n    this.postMessage(message, transfer);\n  };\n  return worker;\n}\n\nfunction setupWorkerThreadWorker(script, WorkerThreads, options) {\n  // validate the options right before creating the worker thread (not when creating the pool)\n  validateOptions(options?.workerThreadOpts, workerThreadOptsNames, 'workerThreadOpts')\n\n  var worker = new WorkerThreads.Worker(script, {\n    stdout: options?.emitStdStreams ?? false, // pipe worker.STDOUT to process.STDOUT if not requested\n    stderr: options?.emitStdStreams ?? false,  // pipe worker.STDERR to process.STDERR if not requested\n    ...options?.workerThreadOpts\n  });\n  worker.isWorkerThread = true;\n  worker.send = function(message, transfer) {\n    this.postMessage(message, transfer);\n  };\n\n  worker.kill = function() {\n    this.terminate();\n    return true;\n  };\n\n  worker.disconnect = function() {\n    this.terminate();\n  };\n\n  if (options?.emitStdStreams) {\n    worker.stdout.on('data', (data) => worker.emit(\"stdout\", data))\n    worker.stderr.on('data', (data) => worker.emit(\"stderr\", data))\n  }\n\n  return worker;\n}\n\nfunction setupProcessWorker(script, options, child_process) {\n  // validate the options right before creating the child process (not when creating the pool)\n  validateOptions(options.forkOpts, forkOptsNames, 'forkOpts')\n\n  // no WorkerThreads, fallback to sub-process based workers\n  var worker = child_process.fork(\n    script,\n    options.forkArgs,\n    options.forkOpts\n  );\n\n  // ignore transfer argument since it is not supported by process\n  var send = worker.send;\n  worker.send = function (message) {\n    return send.call(worker, message);\n  };\n\n  if (options.emitStdStreams) {\n    worker.stdout.on('data', (data) => worker.emit(\"stdout\", data))\n    worker.stderr.on('data', (data) => worker.emit(\"stderr\", data))\n  }\n\n  worker.isChildProcess = true;\n  return worker;\n}\n\n// add debug flags to child processes if the node inspector is active\nfunction resolveForkOptions(opts) {\n  opts = opts || {};\n\n  var processExecArgv = process.execArgv.join(' ');\n  var inspectorActive = processExecArgv.indexOf('--inspect') !== -1;\n  var debugBrk = processExecArgv.indexOf('--debug-brk') !== -1;\n\n  var execArgv = [];\n  if (inspectorActive) {\n    execArgv.push('--inspect=' + opts.debugPort);\n\n    if (debugBrk) {\n      execArgv.push('--debug-brk');\n    }\n  }\n\n  process.execArgv.forEach(function(arg) {\n    if (arg.indexOf('--max-old-space-size') > -1) {\n      execArgv.push(arg)\n    }\n  })\n\n  return Object.assign({}, opts, {\n    forkArgs: opts.forkArgs,\n    forkOpts: Object.assign({}, opts.forkOpts, {\n      execArgv: (opts.forkOpts && opts.forkOpts.execArgv || [])\n      .concat(execArgv),\n      stdio: opts.emitStdStreams ? \"pipe\": undefined\n    })\n  });\n}\n\n/**\n * Converts a serialized error to Error\n * @param {Object} obj Error that has been serialized and parsed to object\n * @return {Error} The equivalent Error.\n */\nfunction objectToError (obj) {\n  var temp = new Error('')\n  var props = Object.keys(obj)\n\n  for (var i = 0; i < props.length; i++) {\n    temp[props[i]] = obj[props[i]]\n  }\n\n  return temp\n}\n\nfunction handleEmittedStdPayload(handler, payload) {\n  // TODO: refactor if parallel task execution gets added\n  Object.values(handler.processing)\n    .forEach(task => task?.options?.on(payload));\n  \n  Object.values(handler.tracking)\n    .forEach(task => task?.options?.on(payload)); \n}\n\n/**\n * A WorkerHandler controls a single worker. This worker can be a child process\n * on node.js or a WebWorker in a browser environment.\n * @param {String} [script] If no script is provided, a default worker with a\n *                          function run will be created.\n * @param {import('./types.js').WorkerPoolOptions} [_options] See docs\n * @constructor\n */\nfunction WorkerHandler(script, _options) {\n  var me = this;\n  var options = _options || {};\n\n  this.script = script || getDefaultWorker();\n  this.worker = setupWorker(this.script, options);\n  this.debugPort = options.debugPort;\n  this.forkOpts = options.forkOpts;\n  this.forkArgs = options.forkArgs;\n  this.workerOpts = options.workerOpts;\n  this.workerThreadOpts = options.workerThreadOpts\n  this.workerTerminateTimeout = options.workerTerminateTimeout;\n\n  // The ready message is only sent if the worker.add method is called (And the default script is not used)\n  if (!script) {\n    this.worker.ready = true;\n  }\n\n  // queue for requests that are received before the worker is ready\n  this.requestQueue = [];\n\n  this.worker.on(\"stdout\", function (data) {\n    handleEmittedStdPayload(me, {\"stdout\": data.toString()})\n  })\n  this.worker.on(\"stderr\", function (data) {\n    handleEmittedStdPayload(me, {\"stderr\": data.toString()})\n  })\n\n  this.worker.on('message', function (response) {\n    if (me.terminated) {\n      return;\n    }\n    if (typeof response === 'string' && response === 'ready') {\n      me.worker.ready = true;\n      dispatchQueuedRequests();\n    } else {\n      // find the task from the processing queue, and run the tasks callback\n      var id = response.id;\n      var task = me.processing[id];\n      if (task !== undefined) {\n        if (response.isEvent) {\n          if (task.options && typeof task.options.on === 'function') {\n            task.options.on(response.payload);\n          }\n        } else {\n          // remove the task from the queue\n          delete me.processing[id];\n\n          // test if we need to terminate\n          if (me.terminating === true) {\n            // complete worker termination if all tasks are finished\n            me.terminate();\n          }\n\n          // resolve the task's promise\n          if (response.error) {\n            task.resolver.reject(objectToError(response.error));\n          }\n          else {\n            task.resolver.resolve(response.result);\n          }\n        }\n      } else {\n        // if the task is not the current, it might be tracked for cleanup\n        var task = me.tracking[id];\n        if (task !== undefined) {\n          if (response.isEvent) {\n            if (task.options && typeof task.options.on === 'function') {\n              task.options.on(response.payload);\n            }\n          }\n        } \n      }\n\n      if (response.method === CLEANUP_METHOD_ID) {\n        var trackedTask = me.tracking[response.id];\n        if (trackedTask !== undefined) {\n          if (response.error) {\n            clearTimeout(trackedTask.timeoutId);\n            trackedTask.resolver.reject(objectToError(response.error))\n          } else {\n            me.tracking && clearTimeout(trackedTask.timeoutId);\n            trackedTask.resolver.resolve(trackedTask.result);            \n          }\n        }\n        delete me.tracking[id];\n      }\n    }\n  });\n\n  // reject all running tasks on worker error\n  function onError(error) {\n    me.terminated = true;\n\n    for (var id in me.processing) {\n      if (me.processing[id] !== undefined) {\n        me.processing[id].resolver.reject(error);\n      }\n    }\n    \n    me.processing = Object.create(null);\n  }\n\n  // send all queued requests to worker\n  function dispatchQueuedRequests()\n  {\n    for(const request of me.requestQueue.splice(0)) {\n      me.worker.send(request.message, request.transfer);\n    }\n  }\n\n  var worker = this.worker;\n  // listen for worker messages error and exit\n  this.worker.on('error', onError);\n  this.worker.on('exit', function (exitCode, signalCode) {\n    var message = 'Workerpool Worker terminated Unexpectedly\\n';\n\n    message += '    exitCode: `' + exitCode + '`\\n';\n    message += '    signalCode: `' + signalCode + '`\\n';\n\n    message += '    workerpool.script: `' +  me.script + '`\\n';\n    message += '    spawnArgs: `' +  worker.spawnargs + '`\\n';\n    message += '    spawnfile: `' + worker.spawnfile + '`\\n'\n\n    message += '    stdout: `' + worker.stdout + '`\\n'\n    message += '    stderr: `' + worker.stderr + '`\\n'\n\n    onError(new Error(message));\n  });\n\n  this.processing = Object.create(null); // queue with tasks currently in progress\n  this.tracking = Object.create(null); // queue with tasks being monitored for cleanup status\n  this.terminating = false;\n  this.terminated = false;\n  this.cleaning = false;\n  this.terminationHandler = null;\n  this.lastId = 0;\n}\n\n/**\n * Get a list with methods available on the worker.\n * @return {Promise.<String[], Error>} methods\n */\nWorkerHandler.prototype.methods = function () {\n  return this.exec('methods');\n};\n\n/**\n * Execute a method with given parameters on the worker\n * @param {String} method\n * @param {Array} [params]\n * @param {{resolve: Function, reject: Function}} [resolver]\n * @param {import('./types.js').ExecOptions}  [options]\n * @return {Promise.<*, Error>} result\n */\nWorkerHandler.prototype.exec = function(method, params, resolver, options) {\n  if (!resolver) {\n    resolver = Promise.defer();\n  }\n\n  // generate a unique id for the task\n  var id = ++this.lastId;\n\n  // register a new task as being in progress\n  this.processing[id] = {\n    id: id,\n    resolver: resolver,\n    options: options\n  };\n\n  // build a JSON-RPC request\n  var request = {\n    message: {\n      id: id,\n      method: method,\n      params: params\n    },\n    transfer: options && options.transfer\n  };\n\n  if (this.terminated) {\n    resolver.reject(new Error('Worker is terminated'));\n  } else if (this.worker.ready) {\n    // send the request to the worker\n    this.worker.send(request.message, request.transfer);\n  } else {\n    this.requestQueue.push(request);\n  }\n\n  // on cancellation, force the worker to terminate\n  var me = this;\n  return resolver.promise.catch(function (error) {\n    if (error instanceof Promise.CancellationError || error instanceof Promise.TimeoutError) {\n      me.tracking[id] = {\n        id,\n        resolver: Promise.defer(),\n        options: options,\n      };\n      \n      // remove this task from the queue. It is already rejected (hence this\n      // catch event), and else it will be rejected again when terminating\n      delete me.processing[id];\n\n      me.tracking[id].resolver.promise = me.tracking[id].resolver.promise.catch(function(err) {\n        delete me.tracking[id];\n\n        var promise = me.terminateAndNotify(true)\n          .then(function() { \n            throw err;\n          }, function(err) {\n            throw err;\n          });\n\n        return promise;\n      });\n \n      me.worker.send({\n        id,\n        method: CLEANUP_METHOD_ID \n      });\n      \n      \n      /**\n        * Sets a timeout to reject the cleanup operation if the message sent to the worker\n        * does not receive a response. see worker.tryCleanup for worker cleanup operations.\n        * Here we use the workerTerminateTimeout as the worker will be terminated if the timeout does invoke.\n        * \n        * We need this timeout in either case of a Timeout or Cancellation Error as if\n        * the worker does not send a message we still need to give a window of time for a response.\n        * \n        * The workerTermniateTimeout is used here if this promise is rejected the worker cleanup\n        * operations will occure.\n      */\n      me.tracking[id].timeoutId = setTimeout(function() {\n          me.tracking[id].resolver.reject(error);\n      }, me.workerTerminateTimeout);\n\n      return me.tracking[id].resolver.promise;\n    } else {\n      throw error;\n    }\n  })\n};\n\n/**\n * Test whether the worker is processing any tasks or cleaning up before termination.\n * @return {boolean} Returns true if the worker is busy\n */\nWorkerHandler.prototype.busy = function () {\n  return this.cleaning || Object.keys(this.processing).length > 0;\n};\n\n/**\n * Terminate the worker.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {function} [callback=null] If provided, will be called when process terminates.\n */\nWorkerHandler.prototype.terminate = function (force, callback) {\n  var me = this;\n  if (force) {\n    // cancel all tasks in progress\n    for (var id in this.processing) {\n      if (this.processing[id] !== undefined) {\n        this.processing[id].resolver.reject(new Error('Worker terminated'));\n      }\n    }\n\n    this.processing = Object.create(null);\n  }\n\n  // If we are terminating, cancel all tracked task for cleanup\n  for (var task of Object.values(me.tracking)) {\n    clearTimeout(task.timeoutId);\n    task.resolver.reject(new Error('Worker Terminating'));\n  }\n\n  me.tracking = Object.create(null);\n\n  if (typeof callback === 'function') {\n    this.terminationHandler = callback;\n  }\n  if (!this.busy()) {\n    // all tasks are finished. kill the worker\n    var cleanup = function(err) {\n      me.terminated = true;\n      me.cleaning = false;\n\n      if (me.worker != null && me.worker.removeAllListeners) {\n        // removeAllListeners is only available for child_process\n        me.worker.removeAllListeners('message');\n      }\n      me.worker = null;\n      me.terminating = false;\n      if (me.terminationHandler) {\n        me.terminationHandler(err, me);\n      } else if (err) {\n        throw err;\n      }\n    }\n\n    if (this.worker) {\n      if (typeof this.worker.kill === 'function') {\n        if (this.worker.killed) {\n          cleanup(new Error('worker already killed!'));\n          return;\n        }\n\n        // child process and worker threads\n        var cleanExitTimeout = setTimeout(function() {\n          if (me.worker) {\n            me.worker.kill();\n          }\n        }, this.workerTerminateTimeout);\n\n        this.worker.once('exit', function() {\n          clearTimeout(cleanExitTimeout);\n          if (me.worker) {\n            me.worker.killed = true;\n          }\n          cleanup();\n        });\n\n        if (this.worker.ready) {\n          this.worker.send(TERMINATE_METHOD_ID);\n        } else {\n          this.requestQueue.push({ message: TERMINATE_METHOD_ID });\n        }\n\n        // mark that the worker is cleaning up resources\n        // to prevent new tasks from being executed\n        this.cleaning = true;\n        return;\n      }\n      else if (typeof this.worker.terminate === 'function') {\n        this.worker.terminate(); // web worker\n        this.worker.killed = true;\n      }\n      else {\n        throw new Error('Failed to terminate worker');\n      }\n    }\n    cleanup();\n  }\n  else {\n    // we can't terminate immediately, there are still tasks being executed\n    this.terminating = true;\n  }\n};\n\n/**\n * Terminate the worker, returning a Promise that resolves when the termination has been done.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<WorkerHandler, Error>}\n */\nWorkerHandler.prototype.terminateAndNotify = function (force, timeout) {\n  var resolver = Promise.defer();\n  if (timeout) {\n    resolver.promise.timeout(timeout);\n  }\n  this.terminate(force, function(err, worker) {\n    if (err) {\n      resolver.reject(err);\n    } else {\n      resolver.resolve(worker);\n    }\n  });\n  return resolver.promise;\n};\n\nmodule.exports = WorkerHandler;\nmodule.exports._tryRequireWorkerThreads = tryRequireWorkerThreads;\nmodule.exports._setupProcessWorker = setupProcessWorker;\nmodule.exports._setupBrowserWorker = setupBrowserWorker;\nmodule.exports._setupWorkerThreadWorker = setupWorkerThreadWorker;\nmodule.exports.ensureWorkerThreads = ensureWorkerThreads;\n", "'use strict';\n\nvar MAX_PORTS = 65535;\nmodule.exports = DebugPortAllocator;\nfunction DebugPortAllocator() {\n  this.ports = Object.create(null);\n  this.length = 0;\n}\n\nDebugPortAllocator.prototype.nextAvailableStartingAt = function(starting) {\n  while (this.ports[starting] === true) {\n    starting++;\n  }\n\n  if (starting >= MAX_PORTS) {\n    throw new Error('WorkerPool debug port limit reached: ' + starting + '>= ' + MAX_PORTS );\n  }\n\n  this.ports[starting] = true;\n  this.length++;\n  return starting;\n};\n\nDebugPortAllocator.prototype.releasePort = function(port) {\n  delete this.ports[port];\n  this.length--;\n};\n\n", "var {Promise} = require('./Promise');\nvar WorkerHandler = require('./WorkerHandler');\nvar environment = require('./environment');\nvar DebugPortAllocator = require('./debug-port-allocator');\nvar DEBUG_PORT_ALLOCATOR = new DebugPortAllocator();\n/**\n * A pool to manage workers, which can be created using the function workerpool.pool.\n *\n * @param {String} [script]   Optional worker script\n * @param {import('./types.js').WorkerPoolOptions} [options]  See docs\n * @constructor\n */\nfunction Pool(script, options) {\n  if (typeof script === 'string') {\n    /** @readonly */\n    this.script = script || null;\n  }\n  else {\n    this.script = null;\n    options = script;\n  }\n\n  /** @private */\n  this.workers = [];  // queue with all workers\n  /** @private */\n  this.tasks = [];    // queue with tasks awaiting execution\n\n  options = options || {};\n\n  /** @readonly */\n  this.forkArgs = Object.freeze(options.forkArgs || []);\n  /** @readonly */\n  this.forkOpts = Object.freeze(options.forkOpts || {});\n  /** @readonly */\n  this.workerOpts = Object.freeze(options.workerOpts || {});\n  /** @readonly */\n  this.workerThreadOpts = Object.freeze(options.workerThreadOpts || {})\n  /** @private */\n  this.debugPortStart = (options.debugPortStart || 43210);\n  /** @readonly @deprecated */\n  this.nodeWorker = options.nodeWorker;\n  /** @readonly\n   * @type {'auto' | 'web' | 'process' | 'thread'}\n   */\n  this.workerType = options.workerType || options.nodeWorker || 'auto'\n  /** @readonly */\n  this.maxQueueSize = options.maxQueueSize || Infinity;\n  /** @readonly */\n  this.workerTerminateTimeout = options.workerTerminateTimeout || 1000;\n\n  /** @readonly */\n  this.onCreateWorker = options.onCreateWorker || (() => null);\n  /** @readonly */\n  this.onTerminateWorker = options.onTerminateWorker || (() => null);\n\n  /** @readonly */\n  this.emitStdStreams = options.emitStdStreams || false\n\n  // configuration\n  if (options && 'maxWorkers' in options) {\n    validateMaxWorkers(options.maxWorkers);\n    /** @readonly */\n    this.maxWorkers = options.maxWorkers;\n  }\n  else {\n    this.maxWorkers = Math.max((environment.cpus || 4) - 1, 1);\n  }\n\n  if (options && 'minWorkers' in options) {\n    if(options.minWorkers === 'max') {\n      /** @readonly */\n      this.minWorkers = this.maxWorkers;\n    } else {\n      validateMinWorkers(options.minWorkers);\n      this.minWorkers = options.minWorkers;\n      this.maxWorkers = Math.max(this.minWorkers, this.maxWorkers);     // in case minWorkers is higher than maxWorkers\n    }\n    this._ensureMinWorkers();\n  }\n\n  /** @private */\n  this._boundNext = this._next.bind(this);\n\n\n  if (this.workerType === 'thread') {\n    WorkerHandler.ensureWorkerThreads();\n  }\n}\n\n\n/**\n * Execute a function on a worker.\n *\n * Example usage:\n *\n *   var pool = new Pool()\n *\n *   // call a function available on the worker\n *   pool.exec('fibonacci', [6])\n *\n *   // offload a function\n *   function add(a, b) {\n *     return a + b\n *   };\n *   pool.exec(add, [2, 4])\n *       .then(function (result) {\n *         console.log(result); // outputs 6\n *       })\n *       .catch(function(error) {\n *         console.log(error);\n *       });\n * @template { (...args: any[]) => any } T\n * @param {String | T} method  Function name or function.\n *                                    If `method` is a string, the corresponding\n *                                    method on the worker will be executed\n *                                    If `method` is a Function, the function\n *                                    will be stringified and executed via the\n *                                    workers built-in function `run(fn, args)`.\n * @param {Parameters<T> | null} [params]  Function arguments applied when calling the function\n * @param {import('./types.js').ExecOptions} [options]  Options\n * @return {Promise<ReturnType<T>>}\n */\nPool.prototype.exec = function (method, params, options) {\n  // validate type of arguments\n  if (params && !Array.isArray(params)) {\n    throw new TypeError('Array expected as argument \"params\"');\n  }\n\n  if (typeof method === 'string') {\n    var resolver = Promise.defer();\n\n    if (this.tasks.length >= this.maxQueueSize) {\n      throw new Error('Max queue size of ' + this.maxQueueSize + ' reached');\n    }\n\n    // add a new task to the queue\n    var tasks = this.tasks;\n    var task = {\n      method:  method,\n      params:  params,\n      resolver: resolver,\n      timeout: null,\n      options: options\n    };\n    tasks.push(task);\n\n    // replace the timeout method of the Promise with our own,\n    // which starts the timer as soon as the task is actually started\n    var originalTimeout = resolver.promise.timeout;\n    resolver.promise.timeout = function timeout (delay) {\n      if (tasks.indexOf(task) !== -1) {\n        // task is still queued -> start the timer later on\n        task.timeout = delay;\n        return resolver.promise;\n      }\n      else {\n        // task is already being executed -> start timer immediately\n        return originalTimeout.call(resolver.promise, delay);\n      }\n    };\n\n    // trigger task execution\n    this._next();\n\n    return resolver.promise;\n  }\n  else if (typeof method === 'function') {\n    // send stringified function and function arguments to worker\n    return this.exec('run', [String(method), params], options);\n  }\n  else {\n    throw new TypeError('Function or string expected as argument \"method\"');\n  }\n};\n\n/**\n * Create a proxy for current worker. Returns an object containing all\n * methods available on the worker. All methods return promises resolving the methods result.\n * @template { { [k: string]: (...args: any[]) => any } } T\n * @return {Promise<import('./types.js').Proxy<T>, Error>} Returns a promise which resolves with a proxy object\n */\nPool.prototype.proxy = function () {\n  if (arguments.length > 0) {\n    throw new Error('No arguments expected');\n  }\n\n  var pool = this;\n  return this.exec('methods')\n      .then(function (methods) {\n        var proxy = {};\n\n        methods.forEach(function (method) {\n          proxy[method] = function () {\n            return pool.exec(method, Array.prototype.slice.call(arguments));\n          }\n        });\n\n        return proxy;\n      });\n};\n\n/**\n * Creates new array with the results of calling a provided callback function\n * on every element in this array.\n * @param {Array} array\n * @param {function} callback  Function taking two arguments:\n *                             `callback(currentValue, index)`\n * @return {Promise.<Array>} Returns a promise which resolves  with an Array\n *                           containing the results of the callback function\n *                           executed for each of the array elements.\n */\n/* TODO: implement map\nPool.prototype.map = function (array, callback) {\n};\n*/\n\n/**\n * Grab the first task from the queue, find a free worker, and assign the\n * worker to the task.\n * @private\n */\nPool.prototype._next = function () {\n  if (this.tasks.length > 0) {\n    // there are tasks in the queue\n\n    // find an available worker\n    var worker = this._getWorker();\n    if (worker) {\n      // get the first task from the queue\n      var me = this;\n      var task = this.tasks.shift();\n\n      // check if the task is still pending (and not cancelled -> promise rejected)\n      if (task.resolver.promise.pending) {\n        // send the request to the worker\n        var promise = worker.exec(task.method, task.params, task.resolver, task.options)\n          .then(me._boundNext)\n          .catch(function () {\n            // if the worker crashed and terminated, remove it from the pool\n            if (worker.terminated) {\n              return me._removeWorker(worker);\n            }\n          }).then(function() {\n            me._next(); // trigger next task in the queue\n          });\n\n        // start queued timer now\n        if (typeof task.timeout === 'number') {\n          promise.timeout(task.timeout);\n        }\n      } else {\n        // The task taken was already complete (either rejected or resolved), so just trigger next task in the queue\n        me._next();\n      }\n    }\n  }\n};\n\n/**\n * Get an available worker. If no worker is available and the maximum number\n * of workers isn't yet reached, a new worker will be created and returned.\n * If no worker is available and the maximum number of workers is reached,\n * null will be returned.\n *\n * @return {WorkerHandler | null} worker\n * @private\n */\nPool.prototype._getWorker = function() {\n  // find a non-busy worker\n  var workers = this.workers;\n  for (var i = 0; i < workers.length; i++) {\n    var worker = workers[i];\n    if (worker.busy() === false) {\n      return worker;\n    }\n  }\n\n  if (workers.length < this.maxWorkers) {\n    // create a new worker\n    worker = this._createWorkerHandler();\n    workers.push(worker);\n    return worker;\n  }\n\n  return null;\n};\n\n/**\n * Remove a worker from the pool.\n * Attempts to terminate worker if not already terminated, and ensures the minimum\n * pool size is met.\n * @param {WorkerHandler} worker\n * @return {Promise<WorkerHandler>}\n * @private\n */\nPool.prototype._removeWorker = function(worker) {\n  var me = this;\n\n  DEBUG_PORT_ALLOCATOR.releasePort(worker.debugPort);\n  // _removeWorker will call this, but we need it to be removed synchronously\n  this._removeWorkerFromList(worker);\n  // If minWorkers set, spin up new workers to replace the crashed ones\n  this._ensureMinWorkers();\n  // terminate the worker (if not already terminated)\n  return new Promise(function(resolve, reject) {\n    worker.terminate(false, function(err) {\n      me.onTerminateWorker({\n        forkArgs: worker.forkArgs,\n        forkOpts: worker.forkOpts,\n        workerThreadOpts: worker.workerThreadOpts,\n        script: worker.script\n      });\n      if (err) {\n        reject(err);\n      } else {\n        resolve(worker);\n      }\n    });\n  });\n};\n\n/**\n * Remove a worker from the pool list.\n * @param {WorkerHandler} worker\n * @private\n */\nPool.prototype._removeWorkerFromList = function(worker) {\n  // remove from the list with workers\n  var index = this.workers.indexOf(worker);\n  if (index !== -1) {\n    this.workers.splice(index, 1);\n  }\n};\n\n/**\n * Close all active workers. Tasks currently being executed will be finished first.\n * @param {boolean} [force=false]   If false (default), the workers are terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the workers will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<void, Error>}\n */\nPool.prototype.terminate = function (force, timeout) {\n  var me = this;\n\n  // cancel any pending tasks\n  this.tasks.forEach(function (task) {\n    task.resolver.reject(new Error('Pool terminated'));\n  });\n  this.tasks.length = 0;\n\n  var f = function (worker) {\n    DEBUG_PORT_ALLOCATOR.releasePort(worker.debugPort);\n    this._removeWorkerFromList(worker);\n  };\n  var removeWorker = f.bind(this);\n\n  var promises = [];\n  var workers = this.workers.slice();\n  workers.forEach(function (worker) {\n    var termPromise = worker.terminateAndNotify(force, timeout)\n      .then(removeWorker)\n      .always(function() {\n        me.onTerminateWorker({\n          forkArgs: worker.forkArgs,\n          forkOpts: worker.forkOpts,\n          workerThreadOpts: worker.workerThreadOpts,\n          script: worker.script\n        });\n      });\n    promises.push(termPromise);\n  });\n  return Promise.all(promises);\n};\n\n/**\n * Retrieve statistics on tasks and workers.\n * @return {{totalWorkers: number, busyWorkers: number, idleWorkers: number, pendingTasks: number, activeTasks: number}} Returns an object with statistics\n */\nPool.prototype.stats = function () {\n  var totalWorkers = this.workers.length;\n  var busyWorkers = this.workers.filter(function (worker) {\n    return worker.busy();\n  }).length;\n\n  return {\n    totalWorkers:  totalWorkers,\n    busyWorkers:   busyWorkers,\n    idleWorkers:   totalWorkers - busyWorkers,\n\n    pendingTasks:  this.tasks.length,\n    activeTasks:   busyWorkers\n  };\n};\n\n/**\n * Ensures that a minimum of minWorkers is up and running\n * @private\n */\nPool.prototype._ensureMinWorkers = function() {\n  if (this.minWorkers) {\n    for(var i = this.workers.length; i < this.minWorkers; i++) {\n      this.workers.push(this._createWorkerHandler());\n    }\n  }\n};\n\n/**\n * Helper function to create a new WorkerHandler and pass all options.\n * @return {WorkerHandler}\n * @private\n */\nPool.prototype._createWorkerHandler = function () {\n  const overriddenParams = this.onCreateWorker({\n    forkArgs: this.forkArgs,\n    forkOpts: this.forkOpts,\n    workerOpts: this.workerOpts,\n    workerThreadOpts: this.workerThreadOpts,\n    script: this.script\n  }) || {};\n\n  return new WorkerHandler(overriddenParams.script || this.script, {\n    forkArgs: overriddenParams.forkArgs || this.forkArgs,\n    forkOpts: overriddenParams.forkOpts || this.forkOpts,\n    workerOpts: overriddenParams.workerOpts || this.workerOpts,\n    workerThreadOpts: overriddenParams.workerThreadOpts || this.workerThreadOpts,\n    debugPort: DEBUG_PORT_ALLOCATOR.nextAvailableStartingAt(this.debugPortStart),\n    workerType: this.workerType,\n    workerTerminateTimeout: this.workerTerminateTimeout,\n    emitStdStreams: this.emitStdStreams,\n  });\n}\n\n/**\n * Ensure that the maxWorkers option is an integer >= 1\n * @param {*} maxWorkers\n * @returns {boolean} returns true maxWorkers has a valid value\n */\nfunction validateMaxWorkers(maxWorkers) {\n  if (!isNumber(maxWorkers) || !isInteger(maxWorkers) || maxWorkers < 1) {\n    throw new TypeError('Option maxWorkers must be an integer number >= 1');\n  }\n}\n\n/**\n * Ensure that the minWorkers option is an integer >= 0\n * @param {*} minWorkers\n * @returns {boolean} returns true when minWorkers has a valid value\n */\nfunction validateMinWorkers(minWorkers) {\n  if (!isNumber(minWorkers) || !isInteger(minWorkers) || minWorkers < 0) {\n    throw new TypeError('Option minWorkers must be an integer number >= 0');\n  }\n}\n\n/**\n * Test whether a variable is a number\n * @param {*} value\n * @returns {boolean} returns true when value is a number\n */\nfunction isNumber(value) {\n  return typeof value === 'number';\n}\n\n/**\n * Test whether a number is an integer\n * @param {number} value\n * @returns {boolean} Returns true if value is an integer\n */\nfunction isInteger(value) {\n  return Math.round(value) == value;\n}\n\nmodule.exports = Pool;\n", "/**\n * The helper class for transferring data from the worker to the main thread.\n *\n * @param {Object} message The object to deliver to the main thread.\n * @param {Object[]} transfer An array of transferable Objects to transfer ownership of.\n */\nfunction Transfer(message, transfer) {\n  this.message = message;\n  this.transfer = transfer;\n}\n\nmodule.exports = Transfer;\n", "/**\n * worker must be started as a child process or a web worker.\n * It listens for RPC messages from the parent process.\n */\n\nvar Transfer = require('./transfer');\n\n/**\n * worker must handle async cleanup handlers. Use custom Promise implementation. \n*/\nvar Promise = require('./Promise').Promise;\n/**\n * Special message sent by parent which causes the worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n/**\n * Special message by parent which causes a child process worker to perform cleaup\n * steps before determining if the child process worker should be terminated.\n*/\nvar CLEANUP_METHOD_ID = '__workerpool-cleanup__';\n// var nodeOSPlatform = require('./environment').nodeOSPlatform;\n\n\nvar TIMEOUT_DEFAULT = 1_000;\n\n// create a worker API for sending and receiving messages which works both on\n// node.js and in the browser\nvar worker = {\n  exit: function() {}\n};\n\n// api for in worker communication with parent process\n// works in both node.js and the browser\nvar publicWorker = {\n  /**\n   * Registers listeners which will trigger when a task is timed out or cancled. If all listeners resolve, the worker executing the given task will not be terminated.\n   * *Note*: If there is a blocking operation within a listener, the worker will be terminated.\n   * @param {() => Promise<void>} listener\n  */\n  addAbortListener: function(listener) {\n    worker.abortListeners.push(listener);\n  },\n\n  /**\n    * Emit an event from the worker thread to the main thread.\n    * @param {any} payload\n  */\n  emit: worker.emit\n};\n\nif (typeof self !== 'undefined' && typeof postMessage === 'function' && typeof addEventListener === 'function') {\n  // worker in the browser\n  worker.on = function (event, callback) {\n    addEventListener(event, function (message) {\n      callback(message.data);\n    })\n  };\n  worker.send = function (message, transfer) {\n     transfer ? postMessage(message, transfer) : postMessage (message);\n  };\n}\nelse if (typeof process !== 'undefined') {\n  // node.js\n\n  var WorkerThreads;\n  try {\n    WorkerThreads = require('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads, fallback to sub-process based workers\n    } else {\n      throw error;\n    }\n  }\n\n  if (WorkerThreads &&\n    /* if there is a parentPort, we are in a WorkerThread */\n    WorkerThreads.parentPort !== null) {\n    var parentPort  = WorkerThreads.parentPort;\n    worker.send = parentPort.postMessage.bind(parentPort);\n    worker.on = parentPort.on.bind(parentPort);\n    worker.exit = process.exit.bind(process);\n  } else {\n    worker.on = process.on.bind(process);\n    // ignore transfer argument since it is not supported by process\n    worker.send = function (message) {\n      process.send(message);\n    };\n    // register disconnect handler only for subprocess worker to exit when parent is killed unexpectedly\n    worker.on('disconnect', function () {\n      process.exit(1);\n    });\n    worker.exit = process.exit.bind(process);\n  }\n}\nelse {\n  throw new Error('Script must be executed as a worker');\n}\n\nfunction convertError(error) {\n  return Object.getOwnPropertyNames(error).reduce(function(product, name) {\n    return Object.defineProperty(product, name, {\n\tvalue: error[name],\n\tenumerable: true\n    });\n  }, {});\n}\n\n/**\n * Test whether a value is a Promise via duck typing.\n * @param {*} value\n * @returns {boolean} Returns true when given value is an object\n *                    having functions `then` and `catch`.\n */\nfunction isPromise(value) {\n  return value && (typeof value.then === 'function') && (typeof value.catch === 'function');\n}\n\n// functions available externally\nworker.methods = {};\n\n/**\n * Execute a function with provided arguments\n * @param {String} fn     Stringified function\n * @param {Array} [args]  Function arguments\n * @returns {*}\n */\nworker.methods.run = function run(fn, args) {\n  var f = new Function('return (' + fn + ').apply(this, arguments);');\n  f.worker = publicWorker;\n  return f.apply(f, args);\n};\n\n/**\n * Get a list with methods available on this worker\n * @return {String[]} methods\n */\nworker.methods.methods = function methods() {\n  return Object.keys(worker.methods);\n};\n\n/**\n * Custom handler for when the worker is terminated.\n */\nworker.terminationHandler = undefined;\n\nworker.abortListenerTimeout = TIMEOUT_DEFAULT;\n\n/**\n * Abort handlers for resolving errors which may cause a timeout or cancellation\n * to occur from a worker context\n */\nworker.abortListeners = [];\n\n/**\n * Cleanup and exit the worker.\n * @param {Number} code \n * @returns {Promise<void>}\n */\nworker.terminateAndExit = function(code) {\n  var _exit = function() {\n    worker.exit(code);\n  }\n\n  if(!worker.terminationHandler) {\n    return _exit();\n  }\n  \n  var result = worker.terminationHandler(code);\n  if (isPromise(result)) {\n    result.then(_exit, _exit);\n\n    return result;\n  } else {\n    _exit();\n    return new Promise(function (_resolve, reject) {\n      reject(new Error(\"Worker terminating\"));\n    });\n  }\n}\n\n\n\n/**\n  * Called within the worker message handler to run abort handlers if registered to perform cleanup operations.\n  * @param {Integer} [requestId] id of task which is currently executing in the worker\n  * @return {Promise<void>}\n*/\nworker.cleanup = function(requestId) {\n\n  if (!worker.abortListeners.length) {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: convertError(new Error('Worker terminating')),\n    });\n\n    // If there are no handlers registered, reject the promise with an error as we want the handler to be notified\n    // that cleanup should begin and the handler should be GCed.\n    return new Promise(function(resolve) { resolve(); });\n  }\n  \n\n  var _exit = function() {\n    worker.exit();\n  }\n\n  var _abort = function() {\n    if (!worker.abortListeners.length) {\n      worker.abortListeners = [];\n    }\n  }\n\n  const promises = worker.abortListeners.map(listener => listener());\n  let timerId;\n  const timeoutPromise = new Promise((_resolve, reject) => {\n    timerId = setTimeout(function () { \n      reject(new Error('Timeout occured waiting for abort handler, killing worker'));\n    }, worker.abortListenerTimeout);\n  });\n\n  // Once a promise settles we need to clear the timeout to prevet fulfulling the promise twice \n  const settlePromise = Promise.all(promises).then(function() {\n    clearTimeout(timerId);\n    _abort();\n  }, function() {\n    clearTimeout(timerId);\n    _exit();\n  });\n\n  // Returns a promise which will result in one of the following cases\n  // - Resolve once all handlers resolve\n  // - Reject if one or more handlers exceed the 'abortListenerTimeout' interval\n  // - Reject if one or more handlers reject\n  // Upon one of the above cases a message will be sent to the handler with the result of the handler execution\n  // which will either kill the worker if the result contains an error, or \n  return Promise.all([\n    settlePromise,\n    timeoutPromise\n  ]).then(function() {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: null,\n    });\n  }, function(err) {\n    worker.send({\n      id: requestId,\n      method: CLEANUP_METHOD_ID,\n      error: err ? convertError(err) : null,\n    });\n  });\n}\n\nvar currentRequestId = null;\n\nworker.on('message', function (request) {\n  if (request === TERMINATE_METHOD_ID) {\n    return worker.terminateAndExit(0);\n  }\n\n  if (request.method === CLEANUP_METHOD_ID) {\n    return worker.cleanup(request.id);\n  }\n\n  try {\n    var method = worker.methods[request.method];\n\n    if (method) {\n      currentRequestId = request.id;\n      \n      // execute the function\n      var result = method.apply(method, request.params);\n\n      if (isPromise(result)) {\n        // promise returned, resolve this and then return\n        result\n            .then(function (result) {\n              if (result instanceof Transfer) {\n                worker.send({\n                  id: request.id,\n                  result: result.message,\n                  error: null\n                }, result.transfer);\n              } else {\n                worker.send({\n                  id: request.id,\n                  result: result,\n                  error: null\n                });\n              }\n              currentRequestId = null;\n            })\n            .catch(function (err) {\n              worker.send({\n                id: request.id,\n                result: null,\n                error: convertError(err),\n              });\n              currentRequestId = null;\n            });\n      }\n      else {\n        // immediate result\n        if (result instanceof Transfer) {\n          worker.send({\n            id: request.id,\n            result: result.message,\n            error: null\n          }, result.transfer);\n        } else {\n          worker.send({\n            id: request.id,\n            result: result,\n            error: null\n          });\n        }\n\n        currentRequestId = null;\n      }\n    }\n    else {\n      throw new Error('Unknown method \"' + request.method + '\"');\n    }\n  }\n  catch (err) {\n    worker.send({\n      id: request.id,\n      result: null,\n      error: convertError(err)\n    });\n  }\n});\n\n/**\n * Register methods to the worker\n * @param {Object} [methods]\n * @param {import('./types.js').WorkerRegisterOptions} [options]\n */\nworker.register = function (methods, options) {\n\n  if (methods) {\n    for (var name in methods) {\n      if (methods.hasOwnProperty(name)) {\n        worker.methods[name] = methods[name];\n        worker.methods[name].worker = publicWorker;\n      }\n    }\n  }\n\n  if (options) {\n    worker.terminationHandler = options.onTerminate;\n    // register listener timeout or default to 1 second\n    worker.abortListenerTimeout = options.abortListenerTimeout || TIMEOUT_DEFAULT;\n  }\n\n  worker.send('ready');\n};\n\nworker.emit = function (payload) {\n  if (currentRequestId) {\n    if (payload instanceof Transfer) {\n      worker.send({\n        id: currentRequestId,\n        isEvent: true,\n        payload: payload.message\n      }, payload.transfer);\n      return;\n    }\n\n    worker.send({\n      id: currentRequestId,\n      isEvent: true,\n      payload\n    });\n  }\n};\n\n\nif (typeof exports !== 'undefined') {\n  exports.add = worker.register;\n  exports.emit = worker.emit;\n}\n", "const {platform, isMainThread, cpus} = require('./environment');\n\n/** @typedef {import(\"./Pool\")} Pool */\n/** @typedef {import(\"./types.js\").WorkerPoolOptions} WorkerPoolOptions */\n/** @typedef {import(\"./types.js\").WorkerRegisterOptions} WorkerRegisterOptions */\n\n/**\n * @template { { [k: string]: (...args: any[]) => any } } T\n * @typedef {import('./types.js').Proxy<T>} Proxy<T>\n */\n\n/**\n * @overload\n * Create a new worker pool\n * @param {WorkerPoolOptions} [script]\n * @returns {Pool} pool\n */\n/**\n * @overload\n * Create a new worker pool\n * @param {string} [script]\n * @param {WorkerPoolOptions} [options]\n * @returns {Pool} pool\n */\nfunction pool(script, options) {\n  var Pool = require('./Pool');\n\n  return new Pool(script, options);\n};\nexports.pool = pool;\n\n/**\n * Create a worker and optionally register a set of methods to the worker.\n * @param {{ [k: string]: (...args: any[]) => any }} [methods]\n * @param {WorkerRegisterOptions} [options]\n */\nfunction worker(methods, options) {\n  var worker = require('./worker');\n  worker.add(methods, options);\n};\nexports.worker = worker;\n\n/**\n * Sends an event to the parent worker pool.\n * @param {any} payload \n */\nfunction workerEmit(payload) {\n  var worker = require('./worker');\n  worker.emit(payload);\n};\nexports.workerEmit = workerEmit;\n\nconst {Promise} = require('./Promise');\nexports.Promise = Promise;\n\nexports.Transfer = require('./transfer');\n\nexports.platform = platform;\nexports.isMainThread = isMainThread;\nexports.cpus = cpus;\n"], "names": ["isNode", "nodeProcess", "versions", "node", "module", "exports", "platform", "process", "worker_threads", "require", "isMainThread", "connected", "Window", "cpus", "self", "navigator", "hardwareConcurrency", "length", "Promise", "handler", "parent", "me", "SyntaxError", "_onSuccess", "_onFail", "resolved", "rejected", "pending", "Symbol", "toStringTag", "_process", "onSuccess", "onFail", "push", "then", "resolve", "reject", "s", "_then", "f", "_resolve", "result", "for<PERSON>ach", "fn", "_reject", "error", "cancel", "CancellationError", "timeout", "delay", "timer", "setTimeout", "TimeoutError", "always", "clearTimeout", "callback", "res", "prototype", "finally", "final", "all", "promises", "remaining", "results", "p", "i", "defer", "resolver", "promise", "message", "stack", "Error", "constructor", "name", "_Promise", "validateOptions", "options", "allowedOptionNames", "objectName", "optionNames", "Object", "keys", "unknownOptionName", "find", "optionName", "includes", "illegalOptionName", "allowedOptionName", "workerOptsNames", "forkOptsNames", "workerThreadOptsNames", "embeddedWorker", "_require$$", "require$$0", "environment", "require$$1", "_require$$2", "require$$2", "TERMINATE_METHOD_ID", "CLEANUP_METHOD_ID", "ensureWorkerThreads", "WorkerThreads", "tryRequireWorkerThreads", "ensureWebWorker", "Worker", "_typeof", "code", "getDefaultWorker", "Blob", "window", "URL", "createObjectURL", "blob", "require$$3", "type", "__dirname", "setupWorker", "script", "workerType", "setupBrowserWorker", "workerOpts", "setupWorkerThreadWorker", "setupProcessWorker", "resolveForkOptions", "worker", "isBrowserWorker", "on", "event", "addEventListener", "data", "send", "transfer", "postMessage", "_options$emitStdStrea", "_options$emitStdStrea2", "workerThreadOpts", "_objectSpread", "stdout", "emitStdStreams", "stderr", "isWorkerThread", "kill", "terminate", "disconnect", "emit", "child_process", "forkOpts", "fork", "forkArgs", "call", "isChildProcess", "opts", "processExecArgv", "execArgv", "join", "inspectorActive", "indexOf", "debugBrk", "debugPort", "arg", "assign", "concat", "stdio", "undefined", "objectToError", "obj", "temp", "props", "handleEmittedStdPayload", "payload", "values", "processing", "task", "_task$options", "tracking", "_task$options2", "Worker<PERSON><PERSON>ler", "_options", "workerTerminateTimeout", "ready", "requestQueue", "toString", "response", "terminated", "dispatchQueuedRequests", "id", "isEvent", "terminating", "method", "trackedTask", "timeoutId", "onError", "create", "_iterator", "_createForOfIteratorHelper", "splice", "_step", "n", "done", "request", "value", "err", "e", "exitCode", "signalCode", "spawnargs", "spawnfile", "cleaning", "<PERSON><PERSON><PERSON><PERSON>", "lastId", "methods", "exec", "params", "catch", "terminateAndNotify", "busy", "force", "_i", "_Object$values", "cleanup", "removeAllListeners", "killed", "cleanExitTimeout", "once", "WorkerHandlerModule", "_tryRequireWorkerThreads", "_setupProcessWorker", "_setupBrowserWorker", "_setupWorkerThreadWorker", "MAX_PORTS", "debugPortAllocator", "DebugPortAllocator", "ports", "nextAvailableStartingAt", "starting", "releasePort", "port", "DEBUG_PORT_ALLOCATOR", "Pool", "workers", "tasks", "freeze", "debugPortStart", "nodeWorker", "maxQueueSize", "Infinity", "onCreateWorker", "onTerminateWorker", "validateMaxWorkers", "maxWorkers", "Math", "max", "minWorkers", "validateMinWorkers", "_ensureMinWorkers", "_boundNext", "_next", "bind", "Array", "isArray", "TypeError", "originalTimeout", "String", "proxy", "arguments", "pool", "slice", "_get<PERSON><PERSON><PERSON>", "shift", "_remove<PERSON><PERSON>ker", "_createWorkerHandler", "_removeWorkerFromList", "index", "removeW<PERSON>ker", "termPromise", "stats", "totalWorkers", "busyWorkers", "filter", "idleWorkers", "pendingTasks", "activeTasks", "overriddenParams", "isNumber", "isInteger", "round", "Pool_1", "Transfer", "TIMEOUT_DEFAULT", "exit", "publicWorker", "addAbortListener", "listener", "abortListeners", "parentPort", "convertError", "getOwnPropertyNames", "reduce", "product", "defineProperty", "enumerable", "isPromise", "run", "args", "Function", "apply", "abortListenerTimeout", "terminateAndExit", "_exit", "requestId", "_abort", "map", "timerId", "timeoutPromise", "settlePromise", "currentRequestId", "register", "hasOwnProperty", "onTerminate", "add", "pool_1", "src", "worker_1", "workerEmit", "workerEmit_1", "require$$4", "platform_1", "isMainThread_1", "cpus_1"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EACA;EACA;EACA,EAAA,IAAIA,MAAM,GAAG,SAATA,MAAMA,CAAaC,WAAW,EAAE;MAClC,OACE,OAAOA,WAAW,KAAK,WAAW,IAClCA,WAAW,CAACC,QAAQ,IAAI,IAAI,IAC5BD,WAAW,CAACC,QAAQ,CAACC,IAAI,IAAI,IAAI,IACjCF,WAAW,GAAG,EAAE,KAAK,kBAAkB;KAE1C;EACDG,EAAAA,MAAA,CAAAC,OAAA,CAAAL,MAAA,GAAwBA,MAAM;;EAE9B;EACAI,EAAAA,MAA0B,CAAAC,OAAA,CAAAC,QAAA,GAAA,OAAOC,OAAO,KAAK,WAAW,IAAIP,MAAM,CAACO,OAAO,CAAC,GACvE,MAAM,GACN,SAAS;;EAEb;EACA;EACA,EAAA,IAAIC,cAAc,GAAGJ,MAAM,CAACC,OAAO,CAACC,QAAQ,KAAK,MAAM,IAAIG,OAAQ,CAAA,gBAAgB,CAAC;EACpFL,EAAAA,MAAA,CAAAC,OAAA,CAAAK,YAAA,GAA8BN,MAAM,CAACC,OAAO,CAACC,QAAQ,KAAK,MAAM,GAC3D,CAAC,CAACE,cAAc,IAAIA,cAAc,CAACE,YAAY,KAAK,CAACH,OAAO,CAACI,SAAS,GACvE,OAAOC,MAAM,KAAK,WAAW;;EAEjC;EACAR,EAAAA,MAAA,CAAAC,OAAA,CAAAQ,IAAA,GAAsBT,MAAM,CAACC,OAAO,CAACC,QAAQ,KAAK,SAAS,GACvDQ,IAAI,CAACC,SAAS,CAACC,mBAAmB,GAClCP,QAAQ,IAAI,CAAC,CAACI,IAAI,EAAE,CAACI,MAAM;;;;;;;;;;;EC1B/B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAA,SAASC,OAAOA,CAACC,OAAO,EAAEC,MAAM,EAAE;MAChC,IAAIC,EAAE,GAAG,IAAI;EAEb,IAAA,IAAI,EAAE,IAAI,YAAYH,OAAO,CAAC,EAAE;EAC9B,MAAA,MAAM,IAAII,WAAW,CAAC,kDAAkD,CAAC;EAC1E;EAED,IAAA,IAAI,OAAOH,OAAO,KAAK,UAAU,EAAE;EACjC,MAAA,MAAM,IAAIG,WAAW,CAAC,qDAAqD,CAAC;EAC7E;MAED,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIC,OAAO,GAAG,EAAE;;EAElB;EACA;EACA;EACA;MACE,IAAI,CAACC,QAAQ,GAAG,KAAK;EACvB;EACA;EACA;MACE,IAAI,CAACC,QAAQ,GAAG,KAAK;EACvB;EACA;EACA;MACE,IAAI,CAACC,OAAO,GAAG,IAAI;EACrB;EACA;EACA;EACE,IAAA,IAAI,CAACC,MAAM,CAACC,WAAW,CAAC,GAAG,SAAS;;EAEtC;EACA;EACA;EACA;EACA;EACA;EACA;MACE,IAAIC,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,SAAS,EAAEC,MAAM,EAAE;EAC1CT,MAAAA,UAAU,CAACU,IAAI,CAACF,SAAS,CAAC;EAC1BP,MAAAA,OAAO,CAACS,IAAI,CAACD,MAAM,CAAC;OACrB;;EAEH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACE,IAAA,IAAI,CAACE,IAAI,GAAG,UAAUH,SAAS,EAAEC,MAAM,EAAE;EACvC,MAAA,OAAO,IAAId,OAAO,CAAC,UAAUiB,OAAO,EAAEC,MAAM,EAAE;EAC5C,QAAA,IAAIC,CAAC,GAAGN,SAAS,GAAGO,KAAK,CAACP,SAAS,EAAEI,OAAO,EAAEC,MAAM,CAAC,GAAGD,OAAO;EAC/D,QAAA,IAAII,CAAC,GAAGP,MAAM,GAAMM,KAAK,CAACN,MAAM,EAAKG,OAAO,EAAEC,MAAM,CAAC,GAAGA,MAAM;EAE9DN,QAAAA,QAAQ,CAACO,CAAC,EAAEE,CAAC,CAAC;SACf,EAAElB,EAAE,CAAC;OACP;;EAEH;EACA;EACA;EACA;EACA;EACE,IAAA,IAAImB,SAAQ,GAAG,SAAXA,QAAQA,CAAaC,MAAM,EAAE;EACnC;QACIpB,EAAE,CAACI,QAAQ,GAAG,IAAI;QAClBJ,EAAE,CAACK,QAAQ,GAAG,KAAK;QACnBL,EAAE,CAACM,OAAO,GAAG,KAAK;EAElBJ,MAAAA,UAAU,CAACmB,OAAO,CAAC,UAAUC,EAAE,EAAE;UAC/BA,EAAE,CAACF,MAAM,CAAC;EAChB,OAAK,CAAC;EAEFX,MAAAA,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,SAAS,EAAEC,MAAM,EAAE;UACtCD,SAAS,CAACU,MAAM,CAAC;SAClB;QAEDD,SAAQ,GAAGI,QAAO,GAAG,SAAVA,OAAOA,GAAe,EAAG;EAEpC,MAAA,OAAOvB,EAAE;OACV;;EAEH;EACA;EACA;EACA;EACA;EACE,IAAA,IAAIuB,QAAO,GAAG,SAAVA,OAAOA,CAAaC,KAAK,EAAE;EACjC;QACIxB,EAAE,CAACI,QAAQ,GAAG,KAAK;QACnBJ,EAAE,CAACK,QAAQ,GAAG,IAAI;QAClBL,EAAE,CAACM,OAAO,GAAG,KAAK;EAElBH,MAAAA,OAAO,CAACkB,OAAO,CAAC,UAAUC,EAAE,EAAE;UAC5BA,EAAE,CAACE,KAAK,CAAC;EACf,OAAK,CAAC;EAEFf,MAAAA,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,SAAS,EAAEC,MAAM,EAAE;UACtCA,MAAM,CAACa,KAAK,CAAC;SACd;QAEDL,SAAQ,GAAGI,QAAO,GAAG,SAAVA,OAAOA,GAAe,EAAG;EAEpC,MAAA,OAAOvB,EAAE;OACV;;EAEH;EACA;EACA;EACA;MACE,IAAI,CAACyB,MAAM,GAAG,YAAY;EACxB,MAAA,IAAI1B,MAAM,EAAE;UACVA,MAAM,CAAC0B,MAAM,EAAE;EAChB,OAAA,MACI;EACHF,QAAAA,QAAO,CAAC,IAAIG,iBAAiB,EAAE,CAAC;EACjC;EAED,MAAA,OAAO1B,EAAE;OACV;;EAEH;EACA;EACA;EACA;EACA;EACA;EACA;EACE,IAAA,IAAI,CAAC2B,OAAO,GAAG,UAAUC,KAAK,EAAE;EAC9B,MAAA,IAAI7B,MAAM,EAAE;EACVA,QAAAA,MAAM,CAAC4B,OAAO,CAACC,KAAK,CAAC;EACtB,OAAA,MACI;EACH,QAAA,IAAIC,KAAK,GAAGC,UAAU,CAAC,YAAY;YACjCP,QAAO,CAAC,IAAIQ,YAAY,CAAC,0BAA0B,GAAGH,KAAK,GAAG,KAAK,CAAC,CAAC;WACtE,EAAEA,KAAK,CAAC;UAET5B,EAAE,CAACgC,MAAM,CAAC,YAAY;YACpBC,YAAY,CAACJ,KAAK,CAAC;EAC3B,SAAO,CAAC;EACH;EAED,MAAA,OAAO7B,EAAE;OACV;;EAEH;MACEF,OAAO,CAAC,UAAUsB,MAAM,EAAE;QACxBD,SAAQ,CAACC,MAAM,CAAC;OACjB,EAAE,UAAUI,KAAK,EAAE;QAClBD,QAAO,CAACC,KAAK,CAAC;EAClB,KAAG,CAAC;EACJ;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAA,SAASP,KAAKA,CAACiB,QAAQ,EAAEpB,OAAO,EAAEC,MAAM,EAAE;MACxC,OAAO,UAAUK,MAAM,EAAE;QACvB,IAAI;EACF,QAAA,IAAIe,GAAG,GAAGD,QAAQ,CAACd,MAAM,CAAC;EAC1B,QAAA,IAAIe,GAAG,IAAI,OAAOA,GAAG,CAACtB,IAAI,KAAK,UAAU,IAAI,OAAOsB,GAAG,CAAC,OAAO,CAAC,KAAK,UAAU,EAAE;EACvF;EACQA,UAAAA,GAAG,CAACtB,IAAI,CAACC,OAAO,EAAEC,MAAM,CAAC;EAC1B,SAAA,MACI;YACHD,OAAO,CAACqB,GAAG,CAAC;EACb;SACF,CACD,OAAOX,KAAK,EAAE;UACZT,MAAM,CAACS,KAAK,CAAC;EACd;OACF;EACH;;EAEA;EACA;EACA;EACA;EACA;EACA;IACA3B,OAAO,CAACuC,SAAS,CAAC,OAAO,CAAC,GAAG,UAAUzB,MAAM,EAAE;EAC7C,IAAA,OAAO,IAAI,CAACE,IAAI,CAAC,IAAI,EAAEF,MAAM,CAAC;KAC/B;;EAED;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACAd,EAAAA,OAAO,CAACuC,SAAS,CAACJ,MAAM,GAAG,UAAUV,EAAE,EAAE;EACvC,IAAA,OAAO,IAAI,CAACT,IAAI,CAACS,EAAE,EAAEA,EAAE,CAAC;KACzB;;EAED;EACA;EACA;EACA;EACA;EACA;EACAzB,EAAAA,OAAO,CAACuC,SAAS,CAACC,OAAO,GAAG,UAAUf,EAAE,EAAE;MACxC,IAAMtB,EAAE,GAAG,IAAI;EAEf,IAAA,IAAMsC,KAAK,GAAG,SAARA,KAAKA,GAAc;EACvB,MAAA,OAAO,IAAIzC,OAAO,CAAC,UAACiB,OAAO,EAAA;UAAA,OAAKA,OAAO,EAAE;EAAA,OAAA,CAAC,CACvCD,IAAI,CAACS,EAAE,CAAC,CACRT,IAAI,CAAC,YAAA;EAAA,QAAA,OAAMb,EAAE;SAAC,CAAA;OAClB;EAED,IAAA,OAAO,IAAI,CAACa,IAAI,CAACyB,KAAK,EAAEA,KAAK,CAAC;KAC/B;;EAED;EACA;EACA;EACA;EACA;EACA;EACAzC,EAAAA,OAAO,CAAC0C,GAAG,GAAG,UAAUC,QAAQ,EAAC;EAC/B,IAAA,OAAO,IAAI3C,OAAO,CAAC,UAAUiB,OAAO,EAAEC,MAAM,EAAE;EAC5C,MAAA,IAAI0B,SAAS,GAAGD,QAAQ,CAAC5C,MAAM;EAC3B8C,QAAAA,OAAO,GAAG,EAAE;EAEhB,MAAA,IAAID,SAAS,EAAE;EACbD,QAAAA,QAAQ,CAACnB,OAAO,CAAC,UAAUsB,CAAC,EAAEC,CAAC,EAAE;EAC/BD,UAAAA,CAAC,CAAC9B,IAAI,CAAC,UAAUO,MAAM,EAAE;EACvBsB,YAAAA,OAAO,CAACE,CAAC,CAAC,GAAGxB,MAAM;EACnBqB,YAAAA,SAAS,EAAE;cACX,IAAIA,SAAS,IAAI,CAAC,EAAE;gBAClB3B,OAAO,CAAC4B,OAAO,CAAC;EACjB;aACF,EAAE,UAAUlB,KAAK,EAAE;EAClBiB,YAAAA,SAAS,GAAG,CAAC;cACb1B,MAAM,CAACS,KAAK,CAAC;EACvB,WAAS,CAAC;EACV,SAAO,CAAC;EACH,OAAA,MACI;UACHV,OAAO,CAAC4B,OAAO,CAAC;EACjB;EACL,KAAG,CAAC;KACH;;EAED;EACA;EACA;EACA;IACA7C,OAAO,CAACgD,KAAK,GAAG,YAAY;MAC1B,IAAIC,QAAQ,GAAG,EAAE;MAEjBA,QAAQ,CAACC,OAAO,GAAG,IAAIlD,OAAO,CAAC,UAAUiB,OAAO,EAAEC,MAAM,EAAE;QACxD+B,QAAQ,CAAChC,OAAO,GAAGA,OAAO;QAC1BgC,QAAQ,CAAC/B,MAAM,GAAGA,MAAM;EAC5B,KAAG,CAAC;EAEF,IAAA,OAAO+B,QAAQ;KAChB;;EAED;EACA;EACA;EACA;EACA;IACA,SAASpB,iBAAiBA,CAACsB,OAAO,EAAE;EAClC,IAAA,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,mBAAmB;MAC7C,IAAI,CAACC,KAAK,GAAI,IAAIC,KAAK,EAAE,CAAED,KAAK;EAClC;EAEAvB,EAAAA,iBAAiB,CAACU,SAAS,GAAG,IAAIc,KAAK,EAAE;EACzCxB,EAAAA,iBAAiB,CAACU,SAAS,CAACe,WAAW,GAAGD,KAAK;EAC/CxB,EAAAA,iBAAiB,CAACU,SAAS,CAACgB,IAAI,GAAG,mBAAmB;IAEtDvD,OAAO,CAAC6B,iBAAiB,GAAGA,iBAAiB;;EAG7C;EACA;EACA;EACA;EACA;IACA,SAASK,YAAYA,CAACiB,OAAO,EAAE;EAC7B,IAAA,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,kBAAkB;MAC5C,IAAI,CAACC,KAAK,GAAI,IAAIC,KAAK,EAAE,CAAED,KAAK;EAClC;EAEAlB,EAAAA,YAAY,CAACK,SAAS,GAAG,IAAIc,KAAK,EAAE;EACpCnB,EAAAA,YAAY,CAACK,SAAS,CAACe,WAAW,GAAGD,KAAK;EAC1CnB,EAAAA,YAAY,CAACK,SAAS,CAACgB,IAAI,GAAG,cAAc;IAE5CvD,OAAO,CAACkC,YAAY,GAAGA,YAAY;IAGnCsB,UAAA,CAAAxD,OAAe,GAAGA,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICjTFyD,eAAA,CAAAA,eAAA,GAAG,SAASA,eAAeA,CAACC,OAAO,EAAEC,kBAAkB,EAAEC,UAAU,EAAE;MAC1F,IAAI,CAACF,OAAO,EAAE;EACZ,MAAA;EACD;MAED,IAAIG,WAAW,GAAGH,OAAO,GAAII,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,GAAG,EAAE;;EAExD;EACE,IAAA,IAAIM,iBAAiB,GAAGH,WAAW,CAACI,IAAI,CAAC,UAAAC,UAAU,EAAA;EAAA,MAAA,OAAI,CAACP,kBAAkB,CAACQ,QAAQ,CAACD,UAAU,CAAC;OAAC,CAAA;EAChG,IAAA,IAAIF,iBAAiB,EAAE;EACrB,MAAA,MAAM,IAAIX,KAAK,CAAC,UAAU,GAAGO,UAAU,GAAG,gCAAgC,GAAGI,iBAAiB,GAAG,GAAG,CAAC;EACtG;;EAEH;MACE,IAAII,iBAAiB,GAAGT,kBAAkB,CAACM,IAAI,CAAC,UAAAI,iBAAiB,EAAI;EACnE,MAAA,OAAOP,MAAM,CAACvB,SAAS,CAAC8B,iBAAiB,CAAC,IAAI,CAACR,WAAW,CAACM,QAAQ,CAACE,iBAAiB,CAAC;EAC1F,KAAG,CAAC;EACF,IAAA,IAAID,iBAAiB,EAAE;EACrB,MAAA,MAAM,IAAIf,KAAK,CAAC,UAAU,GAAGO,UAAU,GAAG,kCAAkC,GAAGQ,iBAAiB,GAAG,aAAa,GAC9G,yFAAyF,GACzF,sFAAsF,CAAC;EAC1F;EAED,IAAA,OAAOV,OAAO;KACf;;EAED;IACAD,eAAA,CAAAa,eAAuB,GAAG,CACxB,aAAa,EAAE,MAAM,EAAE,MAAM,CAAE;;EAEjC;EACAb,EAAAA,eAAA,CAAAc,aAAqB,GAAG,CACtB,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,eAAe,EACxE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,0BAA0B,EAC5E,SAAS,CACV;;EAED;IACAd,eAAA,CAAAe,qBAA6B,GAAG,CAC9B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAC5E,mBAAmB,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM,CAC/D;;;;;;;;;;;;;;EC7CAC,EAAAA,cAAc,GAAG,u7LAAu7L;;;;;;;;ECHx8L,EAAA,IAAAC,UAAA,GAAgBC,iBAAoB;MAA/B3E,OAAO,GAAA0E,UAAA,CAAP1E,OAAO;IACZ,IAAI4E,WAAW,GAAGC,kBAAwB;EAC1C,EAAA,IAAAC,WAAA,GAAiFC,sBAAA,EAA4B;MAAtGtB,eAAe,GAAAqB,WAAA,CAAfrB,eAAe;MAAEc,aAAa,GAAAO,WAAA,CAAbP,aAAa;MAAEC,qBAAqB,GAAAM,WAAA,CAArBN,qBAAqB;MAAEF,eAAe,GAAAQ,WAAA,CAAfR,eAAe;;EAE7E;EACA;EACA;EACA;IACA,IAAIU,mBAAmB,GAAG,0BAA0B;;EAEpD;EACA;EACA;EACA;IACA,IAAIC,iBAAiB,GAAG,wBAAwB;IAEhD,SAASC,mBAAmBA,GAAG;EAC7B,IAAA,IAAIC,aAAa,GAAGC,uBAAuB,EAAE;MAC7C,IAAI,CAACD,aAAa,EAAE;EAClB,MAAA,MAAM,IAAI9B,KAAK,CAAC,+EAA+E,CAAC;EACjG;EAED,IAAA,OAAO8B,aAAa;EACtB;;EAEA;IACA,SAASE,eAAeA,GAAG;EAC3B;MACE,IAAI,OAAOC,MAAM,KAAK,UAAU,KAAK,CAAOA,OAAAA,MAAM,KAAAC,WAAAA,GAAAA,WAAAA,GAAAA,OAAA,CAAND,MAAM,OAAK,QAAQ,IAAI,OAAOA,MAAM,CAAC/C,SAAS,CAACe,WAAW,KAAK,UAAU,CAAC,EAAE;EACtH,MAAA,MAAM,IAAID,KAAK,CAAC,uCAAuC,CAAC;EACzD;EACH;IAEA,SAAS+B,uBAAuBA,GAAG;MACjC,IAAI;QACF,OAAO7F,OAAA,CAAQ,gBAAgB,CAAC;OACjC,CAAC,OAAMoC,KAAK,EAAE;EACb,MAAA,IAAI4D,OAAA,CAAO5D,KAAK,CAAA,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAAC6D,IAAI,KAAK,kBAAkB,EAAE;EAC1F;EACM,QAAA,OAAO,IAAI;EACjB,OAAK,MAAM;EACL,QAAA,MAAM7D,KAAK;EACZ;EACF;EACH;;EAEA;IACA,SAAS8D,gBAAgBA,GAAG;EAC1B,IAAA,IAAIb,WAAW,CAACxF,QAAQ,KAAK,SAAS,EAAE;EAC1C;EACI,MAAA,IAAI,OAAOsG,IAAI,KAAK,WAAW,EAAE;EAC/B,QAAA,MAAM,IAAIrC,KAAK,CAAC,mCAAmC,CAAC;EACrD;EACD,MAAA,IAAI,CAACsC,MAAM,CAACC,GAAG,IAAI,OAAOD,MAAM,CAACC,GAAG,CAACC,eAAe,KAAK,UAAU,EAAE;EACnE,QAAA,MAAM,IAAIxC,KAAK,CAAC,kDAAkD,CAAC;EACpE;;EAEL;QACI,IAAIyC,IAAI,GAAG,IAAIJ,IAAI,CAAC,CAACK,qBAAA,EAAqC,CAAC,EAAE;EAACC,QAAAA,IAAI,EAAE;EAAiB,OAAC,CAAC;EACvF,MAAA,OAAOL,MAAM,CAACC,GAAG,CAACC,eAAe,CAACC,IAAI,CAAC;EACxC,KAAA,MACI;EACP;QACI,OAAOG,SAAS,GAAG,YAAY;EAChC;EACH;EAEA,EAAA,SAASC,WAAWA,CAACC,MAAM,EAAEzC,OAAO,EAAE;EACpC,IAAA,IAAIA,OAAO,CAAC0C,UAAU,KAAK,KAAK,EAAE;EAAA;EAChCf,MAAAA,eAAe,EAAE;QACjB,OAAOgB,kBAAkB,CAACF,MAAM,EAAEzC,OAAO,CAAC4C,UAAU,EAAEhB,MAAM,CAAC;EACjE,KAAG,MAAM,IAAI5B,OAAO,CAAC0C,UAAU,KAAK,QAAQ,EAAE;EAAA;QAC1CjB,aAAa,GAAGD,mBAAmB,EAAE;EACrC,MAAA,OAAOqB,uBAAuB,CAACJ,MAAM,EAAEhB,aAAa,EAAEzB,OAAO,CAAC;EAClE,KAAG,MAAM,IAAIA,OAAO,CAAC0C,UAAU,KAAK,SAAS,IAAI,CAAC1C,OAAO,CAAC0C,UAAU,EAAE;EAAA;EAClE,MAAA,OAAOI,kBAAkB,CAACL,MAAM,EAAEM,kBAAkB,CAAC/C,OAAO,CAAC,EAAEnE,OAAA,CAAQ,eAAe,CAAC,CAAC;EAC5F,KAAG,MAAM;EAAA;EACL,MAAA,IAAIqF,WAAW,CAACxF,QAAQ,KAAK,SAAS,EAAE;EACtCiG,QAAAA,eAAe,EAAE;UACjB,OAAOgB,kBAAkB,CAACF,MAAM,EAAEzC,OAAO,CAAC4C,UAAU,EAAEhB,MAAM,CAAC;EAC9D,OAAA,MACI;EAAA;EACH,QAAA,IAAIH,aAAa,GAAGC,uBAAuB,EAAE;EAC7C,QAAA,IAAID,aAAa,EAAE;EACjB,UAAA,OAAOoB,uBAAuB,CAACJ,MAAM,EAAEhB,aAAa,EAAEzB,OAAO,CAAC;EACtE,SAAO,MAAM;EACL,UAAA,OAAO8C,kBAAkB,CAACL,MAAM,EAAEM,kBAAkB,CAAC/C,OAAO,CAAC,EAAEnE,OAAA,CAAQ,eAAe,CAAC,CAAC;EACzF;EACF;EACF;EACH;EAEA,EAAA,SAAS8G,kBAAkBA,CAACF,MAAM,EAAEG,UAAU,EAAEhB,MAAM,EAAE;EACxD;EACE7B,IAAAA,eAAe,CAAC6C,UAAU,EAAEhC,eAAe,EAAE,YAAY,CAAC;;EAE5D;MACE,IAAIoC,MAAM,GAAG,IAAIpB,MAAM,CAACa,MAAM,EAAEG,UAAU,CAAC;MAE3CI,MAAM,CAACC,eAAe,GAAG,IAAI;EAC/B;EACED,IAAAA,MAAM,CAACE,EAAE,GAAG,UAAUC,KAAK,EAAExE,QAAQ,EAAE;EACrC,MAAA,IAAI,CAACyE,gBAAgB,CAACD,KAAK,EAAE,UAAU1D,OAAO,EAAE;EAC9Cd,QAAAA,QAAQ,CAACc,OAAO,CAAC4D,IAAI,CAAC;EAC5B,OAAK,CAAC;OACH;EACDL,IAAAA,MAAM,CAACM,IAAI,GAAG,UAAU7D,OAAO,EAAE8D,QAAQ,EAAE;EACzC,MAAA,IAAI,CAACC,WAAW,CAAC/D,OAAO,EAAE8D,QAAQ,CAAC;OACpC;EACD,IAAA,OAAOP,MAAM;EACf;EAEA,EAAA,SAASH,uBAAuBA,CAACJ,MAAM,EAAEhB,aAAa,EAAEzB,OAAO,EAAE;MAAA,IAAAyD,qBAAA,EAAAC,sBAAA;EACjE;EACE3D,IAAAA,eAAe,CAACC,OAAO,KAAPA,IAAAA,IAAAA,OAAO,KAAPA,MAAAA,GAAAA,MAAAA,GAAAA,OAAO,CAAE2D,gBAAgB,EAAE7C,qBAAqB,EAAE,kBAAkB,CAAC;MAErF,IAAIkC,MAAM,GAAG,IAAIvB,aAAa,CAACG,MAAM,CAACa,MAAM,EAAAmB,cAAA,CAAA;EAC1CC,MAAAA,MAAM,EAAAJ,CAAAA,qBAAA,GAAEzD,OAAO,aAAPA,OAAO,KAAA,MAAA,GAAA,MAAA,GAAPA,OAAO,CAAE8D,cAAc,MAAAL,IAAAA,IAAAA,qBAAA,KAAAA,MAAAA,GAAAA,qBAAA,GAAI,KAAK;EAAA;EACxCM,MAAAA,MAAM,EAAAL,CAAAA,sBAAA,GAAE1D,OAAO,aAAPA,OAAO,KAAA,MAAA,GAAA,MAAA,GAAPA,OAAO,CAAE8D,cAAc,MAAA,IAAA,IAAAJ,sBAAA,KAAA,MAAA,GAAAA,sBAAA,GAAI;OAChC1D,EAAAA,OAAO,aAAPA,OAAO,KAAA,MAAA,GAAA,MAAA,GAAPA,OAAO,CAAE2D,gBAAgB,CAC7B,CAAC;MACFX,MAAM,CAACgB,cAAc,GAAG,IAAI;EAC5BhB,IAAAA,MAAM,CAACM,IAAI,GAAG,UAAS7D,OAAO,EAAE8D,QAAQ,EAAE;EACxC,MAAA,IAAI,CAACC,WAAW,CAAC/D,OAAO,EAAE8D,QAAQ,CAAC;OACpC;MAEDP,MAAM,CAACiB,IAAI,GAAG,YAAW;QACvB,IAAI,CAACC,SAAS,EAAE;EAChB,MAAA,OAAO,IAAI;OACZ;MAEDlB,MAAM,CAACmB,UAAU,GAAG,YAAW;QAC7B,IAAI,CAACD,SAAS,EAAE;OACjB;EAED,IAAA,IAAIlE,OAAO,KAAPA,IAAAA,IAAAA,OAAO,eAAPA,OAAO,CAAE8D,cAAc,EAAE;QAC3Bd,MAAM,CAACa,MAAM,CAACX,EAAE,CAAC,MAAM,EAAE,UAACG,IAAI,EAAA;EAAA,QAAA,OAAKL,MAAM,CAACoB,IAAI,CAAC,QAAQ,EAAEf,IAAI,CAAC;SAAC,CAAA;QAC/DL,MAAM,CAACe,MAAM,CAACb,EAAE,CAAC,MAAM,EAAE,UAACG,IAAI,EAAA;EAAA,QAAA,OAAKL,MAAM,CAACoB,IAAI,CAAC,QAAQ,EAAEf,IAAI,CAAC;SAAC,CAAA;EAChE;EAED,IAAA,OAAOL,MAAM;EACf;EAEA,EAAA,SAASF,kBAAkBA,CAACL,MAAM,EAAEzC,OAAO,EAAEqE,aAAa,EAAE;EAC5D;MACEtE,eAAe,CAACC,OAAO,CAACsE,QAAQ,EAAEzD,aAAa,EAAE,UAAU,CAAC;;EAE9D;EACE,IAAA,IAAImC,MAAM,GAAGqB,aAAa,CAACE,IAAI,CAC7B9B,MAAM,EACNzC,OAAO,CAACwE,QAAQ,EAChBxE,OAAO,CAACsE,QACZ,CAAG;;EAEH;EACE,IAAA,IAAIhB,IAAI,GAAGN,MAAM,CAACM,IAAI;EACtBN,IAAAA,MAAM,CAACM,IAAI,GAAG,UAAU7D,OAAO,EAAE;EAC/B,MAAA,OAAO6D,IAAI,CAACmB,IAAI,CAACzB,MAAM,EAAEvD,OAAO,CAAC;OAClC;MAED,IAAIO,OAAO,CAAC8D,cAAc,EAAE;QAC1Bd,MAAM,CAACa,MAAM,CAACX,EAAE,CAAC,MAAM,EAAE,UAACG,IAAI,EAAA;EAAA,QAAA,OAAKL,MAAM,CAACoB,IAAI,CAAC,QAAQ,EAAEf,IAAI,CAAC;SAAC,CAAA;QAC/DL,MAAM,CAACe,MAAM,CAACb,EAAE,CAAC,MAAM,EAAE,UAACG,IAAI,EAAA;EAAA,QAAA,OAAKL,MAAM,CAACoB,IAAI,CAAC,QAAQ,EAAEf,IAAI,CAAC;SAAC,CAAA;EAChE;MAEDL,MAAM,CAAC0B,cAAc,GAAG,IAAI;EAC5B,IAAA,OAAO1B,MAAM;EACf;;EAEA;IACA,SAASD,kBAAkBA,CAAC4B,IAAI,EAAE;EAChCA,IAAAA,IAAI,GAAGA,IAAI,IAAI,EAAE;MAEjB,IAAIC,eAAe,GAAGjJ,OAAO,CAACkJ,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC;MAChD,IAAIC,eAAe,GAAGH,eAAe,CAACI,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE;MACjE,IAAIC,QAAQ,GAAGL,eAAe,CAACI,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE;MAE5D,IAAIH,QAAQ,GAAG,EAAE;EACjB,IAAA,IAAIE,eAAe,EAAE;QACnBF,QAAQ,CAACxH,IAAI,CAAC,YAAY,GAAGsH,IAAI,CAACO,SAAS,CAAC;EAE5C,MAAA,IAAID,QAAQ,EAAE;EACZJ,QAAAA,QAAQ,CAACxH,IAAI,CAAC,aAAa,CAAC;EAC7B;EACF;EAED1B,IAAAA,OAAO,CAACkJ,QAAQ,CAAC/G,OAAO,CAAC,UAASqH,GAAG,EAAE;QACrC,IAAIA,GAAG,CAACH,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAAE,EAAE;EAC5CH,QAAAA,QAAQ,CAACxH,IAAI,CAAC8H,GAAG,CAAC;EACnB;EACL,KAAG,CAAC;MAEF,OAAO/E,MAAM,CAACgF,MAAM,CAAC,EAAE,EAAET,IAAI,EAAE;QAC7BH,QAAQ,EAAEG,IAAI,CAACH,QAAQ;QACvBF,QAAQ,EAAElE,MAAM,CAACgF,MAAM,CAAC,EAAE,EAAET,IAAI,CAACL,QAAQ,EAAE;EACzCO,QAAAA,QAAQ,EAAE,CAACF,IAAI,CAACL,QAAQ,IAAIK,IAAI,CAACL,QAAQ,CAACO,QAAQ,IAAI,EAAE,EACvDQ,MAAM,CAACR,QAAQ,CAAC;EACjBS,QAAAA,KAAK,EAAEX,IAAI,CAACb,cAAc,GAAG,MAAM,GAAEyB;SACtC;EACL,KAAG,CAAC;EACJ;;EAEA;EACA;EACA;EACA;EACA;IACA,SAASC,aAAaA,CAAEC,GAAG,EAAE;EAC3B,IAAA,IAAIC,IAAI,GAAG,IAAI/F,KAAK,CAAC,EAAE,CAAC;EACxB,IAAA,IAAIgG,KAAK,GAAGvF,MAAM,CAACC,IAAI,CAACoF,GAAG,CAAC;EAE5B,IAAA,KAAK,IAAIpG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsG,KAAK,CAACtJ,MAAM,EAAEgD,CAAC,EAAE,EAAE;EACrCqG,MAAAA,IAAI,CAACC,KAAK,CAACtG,CAAC,CAAC,CAAC,GAAGoG,GAAG,CAACE,KAAK,CAACtG,CAAC,CAAC,CAAC;EAC/B;EAED,IAAA,OAAOqG,IAAI;EACb;EAEA,EAAA,SAASE,uBAAuBA,CAACrJ,OAAO,EAAEsJ,OAAO,EAAE;EACnD;MACEzF,MAAM,CAAC0F,MAAM,CAACvJ,OAAO,CAACwJ,UAAU,CAAC,CAC9BjI,OAAO,CAAC,UAAAkI,IAAI,EAAA;EAAA,MAAA,IAAAC,aAAA;EAAA,MAAA,OAAID,IAAI,KAAJA,IAAAA,IAAAA,IAAI,KAAAC,MAAAA,IAAAA,CAAAA,aAAA,GAAJD,IAAI,CAAEhG,OAAO,MAAA,IAAA,IAAAiG,aAAA,KAAbA,MAAAA,GAAAA,MAAAA,GAAAA,aAAA,CAAe/C,EAAE,CAAC2C,OAAO,CAAC;OAAC,CAAA;MAE9CzF,MAAM,CAAC0F,MAAM,CAACvJ,OAAO,CAAC2J,QAAQ,CAAC,CAC5BpI,OAAO,CAAC,UAAAkI,IAAI,EAAA;EAAA,MAAA,IAAAG,cAAA;EAAA,MAAA,OAAIH,IAAI,KAAJA,IAAAA,IAAAA,IAAI,KAAAG,MAAAA,IAAAA,CAAAA,cAAA,GAAJH,IAAI,CAAEhG,OAAO,MAAA,IAAA,IAAAmG,cAAA,KAAbA,MAAAA,GAAAA,MAAAA,GAAAA,cAAA,CAAejD,EAAE,CAAC2C,OAAO,CAAC;OAAC,CAAA;EAChD;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAA,SAASO,eAAaA,CAAC3D,MAAM,EAAE4D,QAAQ,EAAE;MACvC,IAAI5J,EAAE,GAAG,IAAI;EACb,IAAA,IAAIuD,OAAO,GAAGqG,QAAQ,IAAI,EAAE;EAE5B,IAAA,IAAI,CAAC5D,MAAM,GAAGA,MAAM,IAAIV,gBAAgB,EAAE;MAC1C,IAAI,CAACiB,MAAM,GAAGR,WAAW,CAAC,IAAI,CAACC,MAAM,EAAEzC,OAAO,CAAC;EAC/C,IAAA,IAAI,CAACkF,SAAS,GAAGlF,OAAO,CAACkF,SAAS;EAClC,IAAA,IAAI,CAACZ,QAAQ,GAAGtE,OAAO,CAACsE,QAAQ;EAChC,IAAA,IAAI,CAACE,QAAQ,GAAGxE,OAAO,CAACwE,QAAQ;EAChC,IAAA,IAAI,CAAC5B,UAAU,GAAG5C,OAAO,CAAC4C,UAAU;EACpC,IAAA,IAAI,CAACe,gBAAgB,GAAG3D,OAAO,CAAC2D,gBAAgB;EAChD,IAAA,IAAI,CAAC2C,sBAAsB,GAAGtG,OAAO,CAACsG,sBAAsB;;EAE9D;MACE,IAAI,CAAC7D,MAAM,EAAE;EACX,MAAA,IAAI,CAACO,MAAM,CAACuD,KAAK,GAAG,IAAI;EACzB;;EAEH;MACE,IAAI,CAACC,YAAY,GAAG,EAAE;MAEtB,IAAI,CAACxD,MAAM,CAACE,EAAE,CAAC,QAAQ,EAAE,UAAUG,IAAI,EAAE;QACvCuC,uBAAuB,CAACnJ,EAAE,EAAE;EAAC,QAAA,QAAQ,EAAE4G,IAAI,CAACoD,QAAQ;EAAE,OAAC,CAAC;EAC5D,KAAG,CAAC;MACF,IAAI,CAACzD,MAAM,CAACE,EAAE,CAAC,QAAQ,EAAE,UAAUG,IAAI,EAAE;QACvCuC,uBAAuB,CAACnJ,EAAE,EAAE;EAAC,QAAA,QAAQ,EAAE4G,IAAI,CAACoD,QAAQ;EAAE,OAAC,CAAC;EAC5D,KAAG,CAAC;MAEF,IAAI,CAACzD,MAAM,CAACE,EAAE,CAAC,SAAS,EAAE,UAAUwD,QAAQ,EAAE;QAC5C,IAAIjK,EAAE,CAACkK,UAAU,EAAE;EACjB,QAAA;EACD;QACD,IAAI,OAAOD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,OAAO,EAAE;EACxDjK,QAAAA,EAAE,CAACuG,MAAM,CAACuD,KAAK,GAAG,IAAI;EACtBK,QAAAA,sBAAsB,EAAE;EAC9B,OAAK,MAAM;EACX;EACM,QAAA,IAAIC,EAAE,GAAGH,QAAQ,CAACG,EAAE;EACpB,QAAA,IAAIb,IAAI,GAAGvJ,EAAE,CAACsJ,UAAU,CAACc,EAAE,CAAC;UAC5B,IAAIb,IAAI,KAAKT,SAAS,EAAE;YACtB,IAAImB,QAAQ,CAACI,OAAO,EAAE;EACpB,YAAA,IAAId,IAAI,CAAChG,OAAO,IAAI,OAAOgG,IAAI,CAAChG,OAAO,CAACkD,EAAE,KAAK,UAAU,EAAE;gBACzD8C,IAAI,CAAChG,OAAO,CAACkD,EAAE,CAACwD,QAAQ,CAACb,OAAO,CAAC;EAClC;EACX,WAAS,MAAM;EACf;EACU,YAAA,OAAOpJ,EAAE,CAACsJ,UAAU,CAACc,EAAE,CAAC;;EAElC;EACU,YAAA,IAAIpK,EAAE,CAACsK,WAAW,KAAK,IAAI,EAAE;EACvC;gBACYtK,EAAE,CAACyH,SAAS,EAAE;EACf;;EAEX;cACU,IAAIwC,QAAQ,CAACzI,KAAK,EAAE;gBAClB+H,IAAI,CAACzG,QAAQ,CAAC/B,MAAM,CAACgI,aAAa,CAACkB,QAAQ,CAACzI,KAAK,CAAC,CAAC;EACpD,aAAA,MACI;gBACH+H,IAAI,CAACzG,QAAQ,CAAChC,OAAO,CAACmJ,QAAQ,CAAC7I,MAAM,CAAC;EACvC;EACF;EACT,SAAO,MAAM;EACb;EACQ,UAAA,IAAImI,IAAI,GAAGvJ,EAAE,CAACyJ,QAAQ,CAACW,EAAE,CAAC;YAC1B,IAAIb,IAAI,KAAKT,SAAS,EAAE;cACtB,IAAImB,QAAQ,CAACI,OAAO,EAAE;EACpB,cAAA,IAAId,IAAI,CAAChG,OAAO,IAAI,OAAOgG,IAAI,CAAChG,OAAO,CAACkD,EAAE,KAAK,UAAU,EAAE;kBACzD8C,IAAI,CAAChG,OAAO,CAACkD,EAAE,CAACwD,QAAQ,CAACb,OAAO,CAAC;EAClC;EACF;EACF;EACF;EAED,QAAA,IAAIa,QAAQ,CAACM,MAAM,KAAKzF,iBAAiB,EAAE;YACzC,IAAI0F,WAAW,GAAGxK,EAAE,CAACyJ,QAAQ,CAACQ,QAAQ,CAACG,EAAE,CAAC;YAC1C,IAAII,WAAW,KAAK1B,SAAS,EAAE;cAC7B,IAAImB,QAAQ,CAACzI,KAAK,EAAE;EAClBS,cAAAA,YAAY,CAACuI,WAAW,CAACC,SAAS,CAAC;gBACnCD,WAAW,CAAC1H,QAAQ,CAAC/B,MAAM,CAACgI,aAAa,CAACkB,QAAQ,CAACzI,KAAK,CAAC,CAAC;EACtE,aAAW,MAAM;gBACLxB,EAAE,CAACyJ,QAAQ,IAAIxH,YAAY,CAACuI,WAAW,CAACC,SAAS,CAAC;gBAClDD,WAAW,CAAC1H,QAAQ,CAAChC,OAAO,CAAC0J,WAAW,CAACpJ,MAAM,CAAC;EACjD;EACF;EACD,UAAA,OAAOpB,EAAE,CAACyJ,QAAQ,CAACW,EAAE,CAAC;EACvB;EACF;EACL,KAAG,CAAC;;EAEJ;MACE,SAASM,OAAOA,CAAClJ,KAAK,EAAE;QACtBxB,EAAE,CAACkK,UAAU,GAAG,IAAI;EAEpB,MAAA,KAAK,IAAIE,EAAE,IAAIpK,EAAE,CAACsJ,UAAU,EAAE;UAC5B,IAAItJ,EAAE,CAACsJ,UAAU,CAACc,EAAE,CAAC,KAAKtB,SAAS,EAAE;YACnC9I,EAAE,CAACsJ,UAAU,CAACc,EAAE,CAAC,CAACtH,QAAQ,CAAC/B,MAAM,CAACS,KAAK,CAAC;EACzC;EACF;QAEDxB,EAAE,CAACsJ,UAAU,GAAG3F,MAAM,CAACgH,MAAM,CAAC,IAAI,CAAC;EACpC;;EAEH;MACE,SAASR,sBAAsBA,GAC/B;QAAA,IAAAS,SAAA,GAAAC,0BAAA,CACuB7K,EAAE,CAAC+J,YAAY,CAACe,MAAM,CAAC,CAAC,CAAC,CAAA;UAAAC,KAAA;EAAA,MAAA,IAAA;UAA9C,KAAAH,SAAA,CAAA5J,CAAA,EAAA+J,EAAAA,CAAAA,CAAAA,KAAA,GAAAH,SAAA,CAAAI,CAAA,EAAAC,EAAAA,IAAA,GAAgD;EAAA,UAAA,IAAtCC,OAAO,GAAAH,KAAA,CAAAI,KAAA;EACfnL,UAAAA,EAAE,CAACuG,MAAM,CAACM,IAAI,CAACqE,OAAO,CAAClI,OAAO,EAAEkI,OAAO,CAACpE,QAAQ,CAAC;EAClD;EAAA,OAAA,CAAA,OAAAsE,GAAA,EAAA;UAAAR,SAAA,CAAAS,CAAA,CAAAD,GAAA,CAAA;EAAA,OAAA,SAAA;EAAAR,QAAAA,SAAA,CAAA1J,CAAA,EAAA;EAAA;EACF;EAED,IAAA,IAAIqF,MAAM,GAAG,IAAI,CAACA,MAAM;EAC1B;MACE,IAAI,CAACA,MAAM,CAACE,EAAE,CAAC,OAAO,EAAEiE,OAAO,CAAC;MAChC,IAAI,CAACnE,MAAM,CAACE,EAAE,CAAC,MAAM,EAAE,UAAU6E,QAAQ,EAAEC,UAAU,EAAE;QACrD,IAAIvI,OAAO,GAAG,6CAA6C;EAE3DA,MAAAA,OAAO,IAAI,iBAAiB,GAAGsI,QAAQ,GAAG,KAAK;EAC/CtI,MAAAA,OAAO,IAAI,mBAAmB,GAAGuI,UAAU,GAAG,KAAK;EAEnDvI,MAAAA,OAAO,IAAI,0BAA0B,GAAIhD,EAAE,CAACgG,MAAM,GAAG,KAAK;EAC1DhD,MAAAA,OAAO,IAAI,kBAAkB,GAAIuD,MAAM,CAACiF,SAAS,GAAG,KAAK;EACzDxI,MAAAA,OAAO,IAAI,kBAAkB,GAAGuD,MAAM,CAACkF,SAAS,GAAG,KAAK;EAExDzI,MAAAA,OAAO,IAAI,eAAe,GAAGuD,MAAM,CAACa,MAAM,GAAG,KAAK;EAClDpE,MAAAA,OAAO,IAAI,eAAe,GAAGuD,MAAM,CAACe,MAAM,GAAG,KAAK;EAElDoD,MAAAA,OAAO,CAAC,IAAIxH,KAAK,CAACF,OAAO,CAAC,CAAC;EAC/B,KAAG,CAAC;MAEF,IAAI,CAACsG,UAAU,GAAG3F,MAAM,CAACgH,MAAM,CAAC,IAAI,CAAC,CAAC;MACtC,IAAI,CAAClB,QAAQ,GAAG9F,MAAM,CAACgH,MAAM,CAAC,IAAI,CAAC,CAAC;MACpC,IAAI,CAACL,WAAW,GAAG,KAAK;MACxB,IAAI,CAACJ,UAAU,GAAG,KAAK;MACvB,IAAI,CAACwB,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB;;EAEA;EACA;EACA;EACA;EACAjC,EAAAA,eAAa,CAACvH,SAAS,CAACyJ,OAAO,GAAG,YAAY;EAC5C,IAAA,OAAO,IAAI,CAACC,IAAI,CAAC,SAAS,CAAC;KAC5B;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAnC,EAAAA,eAAa,CAACvH,SAAS,CAAC0J,IAAI,GAAG,UAASvB,MAAM,EAAEwB,MAAM,EAAEjJ,QAAQ,EAAES,OAAO,EAAE;MACzE,IAAI,CAACT,QAAQ,EAAE;EACbA,MAAAA,QAAQ,GAAGjD,OAAO,CAACgD,KAAK,EAAE;EAC3B;;EAEH;EACE,IAAA,IAAIuH,EAAE,GAAG,EAAE,IAAI,CAACwB,MAAM;;EAExB;EACE,IAAA,IAAI,CAACtC,UAAU,CAACc,EAAE,CAAC,GAAG;EACpBA,MAAAA,EAAE,EAAEA,EAAE;EACNtH,MAAAA,QAAQ,EAAEA,QAAQ;EAClBS,MAAAA,OAAO,EAAEA;OACV;;EAEH;EACE,IAAA,IAAI2H,OAAO,GAAG;EACZlI,MAAAA,OAAO,EAAE;EACPoH,QAAAA,EAAE,EAAEA,EAAE;EACNG,QAAAA,MAAM,EAAEA,MAAM;EACdwB,QAAAA,MAAM,EAAEA;SACT;EACDjF,MAAAA,QAAQ,EAAEvD,OAAO,IAAIA,OAAO,CAACuD;OAC9B;MAED,IAAI,IAAI,CAACoD,UAAU,EAAE;QACnBpH,QAAQ,CAAC/B,MAAM,CAAC,IAAImC,KAAK,CAAC,sBAAsB,CAAC,CAAC;EACtD,KAAG,MAAM,IAAI,IAAI,CAACqD,MAAM,CAACuD,KAAK,EAAE;EAChC;EACI,MAAA,IAAI,CAACvD,MAAM,CAACM,IAAI,CAACqE,OAAO,CAAClI,OAAO,EAAEkI,OAAO,CAACpE,QAAQ,CAAC;EACvD,KAAG,MAAM;EACL,MAAA,IAAI,CAACiD,YAAY,CAACnJ,IAAI,CAACsK,OAAO,CAAC;EAChC;;EAEH;MACE,IAAIlL,EAAE,GAAG,IAAI;MACb,OAAO8C,QAAQ,CAACC,OAAO,CAACiJ,KAAK,CAAC,UAAUxK,KAAK,EAAE;QAC7C,IAAIA,KAAK,YAAY3B,OAAO,CAAC6B,iBAAiB,IAAIF,KAAK,YAAY3B,OAAO,CAACkC,YAAY,EAAE;EACvF/B,QAAAA,EAAE,CAACyJ,QAAQ,CAACW,EAAE,CAAC,GAAG;EAChBA,UAAAA,EAAE,EAAFA,EAAE;EACFtH,UAAAA,QAAQ,EAAEjD,OAAO,CAACgD,KAAK,EAAE;EACzBU,UAAAA,OAAO,EAAEA;WACV;;EAEP;EACA;EACM,QAAA,OAAOvD,EAAE,CAACsJ,UAAU,CAACc,EAAE,CAAC;UAExBpK,EAAE,CAACyJ,QAAQ,CAACW,EAAE,CAAC,CAACtH,QAAQ,CAACC,OAAO,GAAG/C,EAAE,CAACyJ,QAAQ,CAACW,EAAE,CAAC,CAACtH,QAAQ,CAACC,OAAO,CAACiJ,KAAK,CAAC,UAASZ,GAAG,EAAE;EACtF,UAAA,OAAOpL,EAAE,CAACyJ,QAAQ,CAACW,EAAE,CAAC;YAEtB,IAAIrH,OAAO,GAAG/C,EAAE,CAACiM,kBAAkB,CAAC,IAAI,CAAC,CACtCpL,IAAI,CAAC,YAAW;EACf,YAAA,MAAMuK,GAAG;aACV,EAAE,UAASA,GAAG,EAAE;EACf,YAAA,MAAMA,GAAG;EACrB,WAAW,CAAC;EAEJ,UAAA,OAAOrI,OAAO;EACtB,SAAO,CAAC;EAEF/C,QAAAA,EAAE,CAACuG,MAAM,CAACM,IAAI,CAAC;EACbuD,UAAAA,EAAE,EAAFA,EAAE;EACFG,UAAAA,MAAM,EAAEzF;EAChB,SAAO,CAAC;;EAGR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;UACM9E,EAAE,CAACyJ,QAAQ,CAACW,EAAE,CAAC,CAACK,SAAS,GAAG3I,UAAU,CAAC,YAAW;YAC9C9B,EAAE,CAACyJ,QAAQ,CAACW,EAAE,CAAC,CAACtH,QAAQ,CAAC/B,MAAM,CAACS,KAAK,CAAC;EAChD,SAAO,EAAExB,EAAE,CAAC6J,sBAAsB,CAAC;UAE7B,OAAO7J,EAAE,CAACyJ,QAAQ,CAACW,EAAE,CAAC,CAACtH,QAAQ,CAACC,OAAO;EAC7C,OAAK,MAAM;EACL,QAAA,MAAMvB,KAAK;EACZ;EACL,KAAG,CAAC;KACH;;EAED;EACA;EACA;EACA;EACAmI,EAAAA,eAAa,CAACvH,SAAS,CAAC8J,IAAI,GAAG,YAAY;EACzC,IAAA,OAAO,IAAI,CAACR,QAAQ,IAAI/H,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC0F,UAAU,CAAC,CAAC1J,MAAM,GAAG,CAAC;KAChE;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACA+J,eAAa,CAACvH,SAAS,CAACqF,SAAS,GAAG,UAAU0E,KAAK,EAAEjK,QAAQ,EAAE;MAC7D,IAAIlC,EAAE,GAAG,IAAI;EACb,IAAA,IAAImM,KAAK,EAAE;EACb;EACI,MAAA,KAAK,IAAI/B,EAAE,IAAI,IAAI,CAACd,UAAU,EAAE;UAC9B,IAAI,IAAI,CAACA,UAAU,CAACc,EAAE,CAAC,KAAKtB,SAAS,EAAE;EACrC,UAAA,IAAI,CAACQ,UAAU,CAACc,EAAE,CAAC,CAACtH,QAAQ,CAAC/B,MAAM,CAAC,IAAImC,KAAK,CAAC,mBAAmB,CAAC,CAAC;EACpE;EACF;QAED,IAAI,CAACoG,UAAU,GAAG3F,MAAM,CAACgH,MAAM,CAAC,IAAI,CAAC;EACtC;;EAEH;MACE,KAAAyB,IAAAA,EAAA,MAAAC,cAAA,GAAiB1I,MAAM,CAAC0F,MAAM,CAACrJ,EAAE,CAACyJ,QAAQ,CAAC,EAAA2C,EAAA,GAAAC,cAAA,CAAAzM,MAAA,EAAAwM,EAAA,EAAE,EAAA;EAAxC,MAAA,IAAI7C,IAAI,GAAA8C,cAAA,CAAAD,EAAA,CAAA;EACXnK,MAAAA,YAAY,CAACsH,IAAI,CAACkB,SAAS,CAAC;QAC5BlB,IAAI,CAACzG,QAAQ,CAAC/B,MAAM,CAAC,IAAImC,KAAK,CAAC,oBAAoB,CAAC,CAAC;EACtD;MAEDlD,EAAE,CAACyJ,QAAQ,GAAG9F,MAAM,CAACgH,MAAM,CAAC,IAAI,CAAC;EAEjC,IAAA,IAAI,OAAOzI,QAAQ,KAAK,UAAU,EAAE;QAClC,IAAI,CAACyJ,kBAAkB,GAAGzJ,QAAQ;EACnC;EACD,IAAA,IAAI,CAAC,IAAI,CAACgK,IAAI,EAAE,EAAE;EACpB;EACI,MAAA,IAAII,OAAO,GAAG,SAAVA,OAAOA,CAAYlB,GAAG,EAAE;UAC1BpL,EAAE,CAACkK,UAAU,GAAG,IAAI;UACpBlK,EAAE,CAAC0L,QAAQ,GAAG,KAAK;UAEnB,IAAI1L,EAAE,CAACuG,MAAM,IAAI,IAAI,IAAIvG,EAAE,CAACuG,MAAM,CAACgG,kBAAkB,EAAE;EAC7D;EACQvM,UAAAA,EAAE,CAACuG,MAAM,CAACgG,kBAAkB,CAAC,SAAS,CAAC;EACxC;UACDvM,EAAE,CAACuG,MAAM,GAAG,IAAI;UAChBvG,EAAE,CAACsK,WAAW,GAAG,KAAK;UACtB,IAAItK,EAAE,CAAC2L,kBAAkB,EAAE;EACzB3L,UAAAA,EAAE,CAAC2L,kBAAkB,CAACP,GAAG,EAAEpL,EAAE,CAAC;WAC/B,MAAM,IAAIoL,GAAG,EAAE;EACd,UAAA,MAAMA,GAAG;EACV;SACF;QAED,IAAI,IAAI,CAAC7E,MAAM,EAAE;UACf,IAAI,OAAO,IAAI,CAACA,MAAM,CAACiB,IAAI,KAAK,UAAU,EAAE;EAC1C,UAAA,IAAI,IAAI,CAACjB,MAAM,CAACiG,MAAM,EAAE;EACtBF,YAAAA,OAAO,CAAC,IAAIpJ,KAAK,CAAC,wBAAwB,CAAC,CAAC;EAC5C,YAAA;EACD;;EAET;EACQ,UAAA,IAAIuJ,gBAAgB,GAAG3K,UAAU,CAAC,YAAW;cAC3C,IAAI9B,EAAE,CAACuG,MAAM,EAAE;EACbvG,cAAAA,EAAE,CAACuG,MAAM,CAACiB,IAAI,EAAE;EACjB;EACX,WAAS,EAAE,IAAI,CAACqC,sBAAsB,CAAC;EAE/B,UAAA,IAAI,CAACtD,MAAM,CAACmG,IAAI,CAAC,MAAM,EAAE,YAAW;cAClCzK,YAAY,CAACwK,gBAAgB,CAAC;cAC9B,IAAIzM,EAAE,CAACuG,MAAM,EAAE;EACbvG,cAAAA,EAAE,CAACuG,MAAM,CAACiG,MAAM,GAAG,IAAI;EACxB;EACDF,YAAAA,OAAO,EAAE;EACnB,WAAS,CAAC;EAEF,UAAA,IAAI,IAAI,CAAC/F,MAAM,CAACuD,KAAK,EAAE;EACrB,YAAA,IAAI,CAACvD,MAAM,CAACM,IAAI,CAAChC,mBAAmB,CAAC;EAC/C,WAAS,MAAM;EACL,YAAA,IAAI,CAACkF,YAAY,CAACnJ,IAAI,CAAC;EAAEoC,cAAAA,OAAO,EAAE6B;EAAqB,aAAA,CAAC;EACzD;;EAET;EACA;YACQ,IAAI,CAAC6G,QAAQ,GAAG,IAAI;EACpB,UAAA;WACD,MACI,IAAI,OAAO,IAAI,CAACnF,MAAM,CAACkB,SAAS,KAAK,UAAU,EAAE;EACpD,UAAA,IAAI,CAAClB,MAAM,CAACkB,SAAS,EAAE,CAAC;EACxB,UAAA,IAAI,CAAClB,MAAM,CAACiG,MAAM,GAAG,IAAI;EAC1B,SAAA,MACI;EACH,UAAA,MAAM,IAAItJ,KAAK,CAAC,4BAA4B,CAAC;EAC9C;EACF;EACDoJ,MAAAA,OAAO,EAAE;EACV,KAAA,MACI;EACP;QACI,IAAI,CAAChC,WAAW,GAAG,IAAI;EACxB;KACF;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACAX,eAAa,CAACvH,SAAS,CAAC6J,kBAAkB,GAAG,UAAUE,KAAK,EAAExK,OAAO,EAAE;EACrE,IAAA,IAAImB,QAAQ,GAAGjD,OAAO,CAACgD,KAAK,EAAE;EAC9B,IAAA,IAAIlB,OAAO,EAAE;EACXmB,MAAAA,QAAQ,CAACC,OAAO,CAACpB,OAAO,CAACA,OAAO,CAAC;EAClC;MACD,IAAI,CAAC8F,SAAS,CAAC0E,KAAK,EAAE,UAASf,GAAG,EAAE7E,MAAM,EAAE;EAC1C,MAAA,IAAI6E,GAAG,EAAE;EACPtI,QAAAA,QAAQ,CAAC/B,MAAM,CAACqK,GAAG,CAAC;EAC1B,OAAK,MAAM;EACLtI,QAAAA,QAAQ,CAAChC,OAAO,CAACyF,MAAM,CAAC;EACzB;EACL,KAAG,CAAC;MACF,OAAOzD,QAAQ,CAACC,OAAO;KACxB;IAEa4J,aAAA,CAAA3N,OAAA,GAAG2K,eAAa;EACSgD,EAAAA,aAAA,CAAA3N,OAAA,CAAA4N,wBAAA,GAAG3H,uBAAuB;EAC/B0H,EAAAA,aAAA,CAAA3N,OAAA,CAAA6N,mBAAA,GAAGxG,kBAAkB;EACrBsG,EAAAA,aAAA,CAAA3N,OAAA,CAAA8N,mBAAA,GAAG5G,kBAAkB;EAChByG,EAAAA,aAAA,CAAA3N,OAAA,CAAA+N,wBAAA,GAAG3G,uBAAuB;EACjEuG,EAAAA,aAAA,CAAA3N,OAAA,CAAA+F,mBAAkC,GAAGA,mBAAmB;;;;;;;;;ICzmBxD,IAAIiI,SAAS,GAAG,KAAK;EACPC,EAAAA,kBAAA,GAAGC,kBAAkB;IACnC,SAASA,kBAAkBA,GAAG;MAC5B,IAAI,CAACC,KAAK,GAAGxJ,MAAM,CAACgH,MAAM,CAAC,IAAI,CAAC;MAChC,IAAI,CAAC/K,MAAM,GAAG,CAAC;EACjB;EAEAsN,EAAAA,kBAAkB,CAAC9K,SAAS,CAACgL,uBAAuB,GAAG,UAASC,QAAQ,EAAE;MACxE,OAAO,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,KAAK,IAAI,EAAE;EACpCA,MAAAA,QAAQ,EAAE;EACX;MAED,IAAIA,QAAQ,IAAIL,SAAS,EAAE;QACzB,MAAM,IAAI9J,KAAK,CAAC,uCAAuC,GAAGmK,QAAQ,GAAG,KAAK,GAAGL,SAAS,CAAE;EACzF;EAED,IAAA,IAAI,CAACG,KAAK,CAACE,QAAQ,CAAC,GAAG,IAAI;MAC3B,IAAI,CAACzN,MAAM,EAAE;EACb,IAAA,OAAOyN,QAAQ;KAChB;EAEDH,EAAAA,kBAAkB,CAAC9K,SAAS,CAACkL,WAAW,GAAG,UAASC,IAAI,EAAE;EACxD,IAAA,OAAO,IAAI,CAACJ,KAAK,CAACI,IAAI,CAAC;MACvB,IAAI,CAAC3N,MAAM,EAAE;KACd;;;;;;;;;EC1BD,EAAA,IAAA2E,UAAA,GAAgBC,iBAAoB;MAA/B3E,OAAO,GAAA0E,UAAA,CAAP1E,OAAO;EACZ,EAAA,IAAI8J,aAAa,GAAGjF,oBAAA,EAA0B;IAC9C,IAAID,WAAW,GAAGG,kBAAwB;EAC1C,EAAA,IAAIsI,kBAAkB,GAAGtH,yBAAA,EAAiC;EAC1D,EAAA,IAAI4H,oBAAoB,GAAG,IAAIN,kBAAkB,EAAE;EACnD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAA,SAASO,IAAIA,CAACzH,MAAM,EAAEzC,OAAO,EAAE;EAC7B,IAAA,IAAI,OAAOyC,MAAM,KAAK,QAAQ,EAAE;EAClC;EACI,MAAA,IAAI,CAACA,MAAM,GAAGA,MAAM,IAAI,IAAI;EAC7B,KAAA,MACI;QACH,IAAI,CAACA,MAAM,GAAG,IAAI;EAClBzC,MAAAA,OAAO,GAAGyC,MAAM;EACjB;;EAEH;EACE,IAAA,IAAI,CAAC0H,OAAO,GAAG,EAAE,CAAC;EACpB;EACE,IAAA,IAAI,CAACC,KAAK,GAAG,EAAE,CAAC;;EAEhBpK,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE;;EAEzB;EACE,IAAA,IAAI,CAACwE,QAAQ,GAAGpE,MAAM,CAACiK,MAAM,CAACrK,OAAO,CAACwE,QAAQ,IAAI,EAAE,CAAC;EACvD;EACE,IAAA,IAAI,CAACF,QAAQ,GAAGlE,MAAM,CAACiK,MAAM,CAACrK,OAAO,CAACsE,QAAQ,IAAI,EAAE,CAAC;EACvD;EACE,IAAA,IAAI,CAAC1B,UAAU,GAAGxC,MAAM,CAACiK,MAAM,CAACrK,OAAO,CAAC4C,UAAU,IAAI,EAAE,CAAC;EAC3D;EACE,IAAA,IAAI,CAACe,gBAAgB,GAAGvD,MAAM,CAACiK,MAAM,CAACrK,OAAO,CAAC2D,gBAAgB,IAAI,EAAE,CAAC;EACvE;EACE,IAAA,IAAI,CAAC2G,cAAc,GAAItK,OAAO,CAACsK,cAAc,IAAI,KAAM;EACzD;EACE,IAAA,IAAI,CAACC,UAAU,GAAGvK,OAAO,CAACuK,UAAU;EACtC;EACA;EACA;MACE,IAAI,CAAC7H,UAAU,GAAG1C,OAAO,CAAC0C,UAAU,IAAI1C,OAAO,CAACuK,UAAU,IAAI,MAAM;EACtE;EACE,IAAA,IAAI,CAACC,YAAY,GAAGxK,OAAO,CAACwK,YAAY,IAAIC,QAAQ;EACtD;EACE,IAAA,IAAI,CAACnE,sBAAsB,GAAGtG,OAAO,CAACsG,sBAAsB,IAAI,IAAI;;EAEtE;EACE,IAAA,IAAI,CAACoE,cAAc,GAAG1K,OAAO,CAAC0K,cAAc,IAAK,YAAA;EAAA,MAAA,OAAM,IAAI;OAAC;EAC9D;EACE,IAAA,IAAI,CAACC,iBAAiB,GAAG3K,OAAO,CAAC2K,iBAAiB,IAAK,YAAA;EAAA,MAAA,OAAM,IAAI;OAAC;;EAEpE;EACE,IAAA,IAAI,CAAC7G,cAAc,GAAG9D,OAAO,CAAC8D,cAAc,IAAI,KAAK;;EAEvD;EACE,IAAA,IAAI9D,OAAO,IAAI,YAAY,IAAIA,OAAO,EAAE;EACtC4K,MAAAA,kBAAkB,CAAC5K,OAAO,CAAC6K,UAAU,CAAC;EAC1C;EACI,MAAA,IAAI,CAACA,UAAU,GAAG7K,OAAO,CAAC6K,UAAU;EACrC,KAAA,MACI;EACH,MAAA,IAAI,CAACA,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC7J,WAAW,CAACjF,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EAC3D;EAED,IAAA,IAAI+D,OAAO,IAAI,YAAY,IAAIA,OAAO,EAAE;EACtC,MAAA,IAAGA,OAAO,CAACgL,UAAU,KAAK,KAAK,EAAE;EACrC;EACM,QAAA,IAAI,CAACA,UAAU,GAAG,IAAI,CAACH,UAAU;EACvC,OAAK,MAAM;EACLI,QAAAA,kBAAkB,CAACjL,OAAO,CAACgL,UAAU,CAAC;EACtC,QAAA,IAAI,CAACA,UAAU,GAAGhL,OAAO,CAACgL,UAAU;EACpC,QAAA,IAAI,CAACH,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACH,UAAU,CAAC,CAAC;EAC9D;QACD,IAAI,CAACK,iBAAiB,EAAE;EACzB;;EAEH;MACE,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;EAGvC,IAAA,IAAI,IAAI,CAAC3I,UAAU,KAAK,QAAQ,EAAE;QAChC0D,aAAa,CAAC5E,mBAAmB,EAAE;EACpC;EACH;;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACA0I,IAAI,CAACrL,SAAS,CAAC0J,IAAI,GAAG,UAAUvB,MAAM,EAAEwB,MAAM,EAAExI,OAAO,EAAE;EACzD;MACE,IAAIwI,MAAM,IAAI,CAAC8C,KAAK,CAACC,OAAO,CAAC/C,MAAM,CAAC,EAAE;EACpC,MAAA,MAAM,IAAIgD,SAAS,CAAC,qCAAqC,CAAC;EAC3D;EAED,IAAA,IAAI,OAAOxE,MAAM,KAAK,QAAQ,EAAE;EAC9B,MAAA,IAAIzH,QAAQ,GAAGjD,OAAO,CAACgD,KAAK,EAAE;QAE9B,IAAI,IAAI,CAAC8K,KAAK,CAAC/N,MAAM,IAAI,IAAI,CAACmO,YAAY,EAAE;UAC1C,MAAM,IAAI7K,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC6K,YAAY,GAAG,UAAU,CAAC;EACvE;;EAEL;EACI,MAAA,IAAIJ,KAAK,GAAG,IAAI,CAACA,KAAK;EACtB,MAAA,IAAIpE,IAAI,GAAG;EACTgB,QAAAA,MAAM,EAAGA,MAAM;EACfwB,QAAAA,MAAM,EAAGA,MAAM;EACfjJ,QAAAA,QAAQ,EAAEA,QAAQ;EAClBnB,QAAAA,OAAO,EAAE,IAAI;EACb4B,QAAAA,OAAO,EAAEA;SACV;EACDoK,MAAAA,KAAK,CAAC/M,IAAI,CAAC2I,IAAI,CAAC;;EAEpB;EACA;EACI,MAAA,IAAIyF,eAAe,GAAGlM,QAAQ,CAACC,OAAO,CAACpB,OAAO;QAC9CmB,QAAQ,CAACC,OAAO,CAACpB,OAAO,GAAG,SAASA,OAAOA,CAAEC,KAAK,EAAE;UAClD,IAAI+L,KAAK,CAACpF,OAAO,CAACgB,IAAI,CAAC,KAAK,EAAE,EAAE;EACtC;YACQA,IAAI,CAAC5H,OAAO,GAAGC,KAAK;YACpB,OAAOkB,QAAQ,CAACC,OAAO;EACxB,SAAA,MACI;EACX;YACQ,OAAOiM,eAAe,CAAChH,IAAI,CAAClF,QAAQ,CAACC,OAAO,EAAEnB,KAAK,CAAC;EACrD;SACF;;EAEL;QACI,IAAI,CAAC+M,KAAK,EAAE;QAEZ,OAAO7L,QAAQ,CAACC,OAAO;EACxB,KAAA,MACI,IAAI,OAAOwH,MAAM,KAAK,UAAU,EAAE;EACzC;EACI,MAAA,OAAO,IAAI,CAACuB,IAAI,CAAC,KAAK,EAAE,CAACmD,MAAM,CAAC1E,MAAM,CAAC,EAAEwB,MAAM,CAAC,EAAExI,OAAO,CAAC;EAC3D,KAAA,MACI;EACH,MAAA,MAAM,IAAIwL,SAAS,CAAC,kDAAkD,CAAC;EACxE;KACF;;EAED;EACA;EACA;EACA;EACA;EACA;EACAtB,EAAAA,IAAI,CAACrL,SAAS,CAAC8M,KAAK,GAAG,YAAY;EACjC,IAAA,IAAIC,SAAS,CAACvP,MAAM,GAAG,CAAC,EAAE;EACxB,MAAA,MAAM,IAAIsD,KAAK,CAAC,uBAAuB,CAAC;EACzC;MAED,IAAIkM,IAAI,GAAG,IAAI;MACf,OAAO,IAAI,CAACtD,IAAI,CAAC,SAAS,CAAC,CACtBjL,IAAI,CAAC,UAAUgL,OAAO,EAAE;QACvB,IAAIqD,KAAK,GAAG,EAAE;EAEdrD,MAAAA,OAAO,CAACxK,OAAO,CAAC,UAAUkJ,MAAM,EAAE;EAChC2E,QAAAA,KAAK,CAAC3E,MAAM,CAAC,GAAG,YAAY;EAC1B,UAAA,OAAO6E,IAAI,CAACtD,IAAI,CAACvB,MAAM,EAAEsE,KAAK,CAACzM,SAAS,CAACiN,KAAK,CAACrH,IAAI,CAACmH,SAAS,CAAC,CAAC;WAChE;EACX,OAAS,CAAC;EAEF,MAAA,OAAOD,KAAK;EACpB,KAAO,CAAC;KACP;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACAzB,EAAAA,IAAI,CAACrL,SAAS,CAACuM,KAAK,GAAG,YAAY;EACjC,IAAA,IAAI,IAAI,CAAChB,KAAK,CAAC/N,MAAM,GAAG,CAAC,EAAE;EAC7B;;EAEA;EACI,MAAA,IAAI2G,MAAM,GAAG,IAAI,CAAC+I,UAAU,EAAE;EAC9B,MAAA,IAAI/I,MAAM,EAAE;EAChB;UACM,IAAIvG,EAAE,GAAG,IAAI;UACb,IAAIuJ,IAAI,GAAG,IAAI,CAACoE,KAAK,CAAC4B,KAAK,EAAE;;EAEnC;EACM,QAAA,IAAIhG,IAAI,CAACzG,QAAQ,CAACC,OAAO,CAACzC,OAAO,EAAE;EACzC;EACQ,UAAA,IAAIyC,OAAO,GAAGwD,MAAM,CAACuF,IAAI,CAACvC,IAAI,CAACgB,MAAM,EAAEhB,IAAI,CAACwC,MAAM,EAAExC,IAAI,CAACzG,QAAQ,EAAEyG,IAAI,CAAChG,OAAO,CAAC,CAC7E1C,IAAI,CAACb,EAAE,CAAC0O,UAAU,CAAC,CACnB1C,KAAK,CAAC,YAAY;EAC7B;cACY,IAAIzF,MAAM,CAAC2D,UAAU,EAAE;EACrB,cAAA,OAAOlK,EAAE,CAACwP,aAAa,CAACjJ,MAAM,CAAC;EAChC;EACb,WAAW,CAAC,CAAC1F,IAAI,CAAC,YAAW;EACjBb,YAAAA,EAAE,CAAC2O,KAAK,EAAE,CAAC;EACvB,WAAW,CAAC;;EAEZ;EACQ,UAAA,IAAI,OAAOpF,IAAI,CAAC5H,OAAO,KAAK,QAAQ,EAAE;EACpCoB,YAAAA,OAAO,CAACpB,OAAO,CAAC4H,IAAI,CAAC5H,OAAO,CAAC;EAC9B;EACT,SAAO,MAAM;EACb;YACQ3B,EAAE,CAAC2O,KAAK,EAAE;EACX;EACF;EACF;KACF;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAlB,EAAAA,IAAI,CAACrL,SAAS,CAACkN,UAAU,GAAG,YAAW;EACvC;EACE,IAAA,IAAI5B,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAA,KAAK,IAAI9K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8K,OAAO,CAAC9N,MAAM,EAAEgD,CAAC,EAAE,EAAE;EACvC,MAAA,IAAI2D,MAAM,GAAGmH,OAAO,CAAC9K,CAAC,CAAC;EACvB,MAAA,IAAI2D,MAAM,CAAC2F,IAAI,EAAE,KAAK,KAAK,EAAE;EAC3B,QAAA,OAAO3F,MAAM;EACd;EACF;EAED,IAAA,IAAImH,OAAO,CAAC9N,MAAM,GAAG,IAAI,CAACwO,UAAU,EAAE;EACxC;EACI7H,MAAAA,MAAM,GAAG,IAAI,CAACkJ,oBAAoB,EAAE;EACpC/B,MAAAA,OAAO,CAAC9M,IAAI,CAAC2F,MAAM,CAAC;EACpB,MAAA,OAAOA,MAAM;EACd;EAED,IAAA,OAAO,IAAI;KACZ;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAkH,EAAAA,IAAI,CAACrL,SAAS,CAACoN,aAAa,GAAG,UAASjJ,MAAM,EAAE;MAC9C,IAAIvG,EAAE,GAAG,IAAI;EAEbwN,IAAAA,oBAAoB,CAACF,WAAW,CAAC/G,MAAM,CAACkC,SAAS,CAAC;EACpD;EACE,IAAA,IAAI,CAACiH,qBAAqB,CAACnJ,MAAM,CAAC;EACpC;MACE,IAAI,CAACkI,iBAAiB,EAAE;EAC1B;EACE,IAAA,OAAO,IAAI5O,OAAO,CAAC,UAASiB,OAAO,EAAEC,MAAM,EAAE;EAC3CwF,MAAAA,MAAM,CAACkB,SAAS,CAAC,KAAK,EAAE,UAAS2D,GAAG,EAAE;UACpCpL,EAAE,CAACkO,iBAAiB,CAAC;YACnBnG,QAAQ,EAAExB,MAAM,CAACwB,QAAQ;YACzBF,QAAQ,EAAEtB,MAAM,CAACsB,QAAQ;YACzBX,gBAAgB,EAAEX,MAAM,CAACW,gBAAgB;YACzClB,MAAM,EAAEO,MAAM,CAACP;EACvB,SAAO,CAAC;EACF,QAAA,IAAIoF,GAAG,EAAE;YACPrK,MAAM,CAACqK,GAAG,CAAC;EACnB,SAAO,MAAM;YACLtK,OAAO,CAACyF,MAAM,CAAC;EAChB;EACP,OAAK,CAAC;EACN,KAAG,CAAC;KACH;;EAED;EACA;EACA;EACA;EACA;EACAkH,EAAAA,IAAI,CAACrL,SAAS,CAACsN,qBAAqB,GAAG,UAASnJ,MAAM,EAAE;EACxD;MACE,IAAIoJ,KAAK,GAAG,IAAI,CAACjC,OAAO,CAACnF,OAAO,CAAChC,MAAM,CAAC;EACxC,IAAA,IAAIoJ,KAAK,KAAK,EAAE,EAAE;QAChB,IAAI,CAACjC,OAAO,CAAC5C,MAAM,CAAC6E,KAAK,EAAE,CAAC,CAAC;EAC9B;KACF;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACAlC,IAAI,CAACrL,SAAS,CAACqF,SAAS,GAAG,UAAU0E,KAAK,EAAExK,OAAO,EAAE;MACnD,IAAI3B,EAAE,GAAG,IAAI;;EAEf;EACE,IAAA,IAAI,CAAC2N,KAAK,CAACtM,OAAO,CAAC,UAAUkI,IAAI,EAAE;QACjCA,IAAI,CAACzG,QAAQ,CAAC/B,MAAM,CAAC,IAAImC,KAAK,CAAC,iBAAiB,CAAC,CAAC;EACtD,KAAG,CAAC;EACF,IAAA,IAAI,CAACyK,KAAK,CAAC/N,MAAM,GAAG,CAAC;EAErB,IAAA,IAAIsB,CAAC,GAAG,SAAJA,CAACA,CAAaqF,MAAM,EAAE;EACxBiH,MAAAA,oBAAoB,CAACF,WAAW,CAAC/G,MAAM,CAACkC,SAAS,CAAC;EAClD,MAAA,IAAI,CAACiH,qBAAqB,CAACnJ,MAAM,CAAC;OACnC;EACD,IAAA,IAAIqJ,YAAY,GAAG1O,CAAC,CAAC0N,IAAI,CAAC,IAAI,CAAC;MAE/B,IAAIpM,QAAQ,GAAG,EAAE;MACjB,IAAIkL,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC2B,KAAK,EAAE;EAClC3B,IAAAA,OAAO,CAACrM,OAAO,CAAC,UAAUkF,MAAM,EAAE;EAChC,MAAA,IAAIsJ,WAAW,GAAGtJ,MAAM,CAAC0F,kBAAkB,CAACE,KAAK,EAAExK,OAAO,CAAC,CACxDd,IAAI,CAAC+O,YAAY,CAAC,CAClB5N,MAAM,CAAC,YAAW;UACjBhC,EAAE,CAACkO,iBAAiB,CAAC;YACnBnG,QAAQ,EAAExB,MAAM,CAACwB,QAAQ;YACzBF,QAAQ,EAAEtB,MAAM,CAACsB,QAAQ;YACzBX,gBAAgB,EAAEX,MAAM,CAACW,gBAAgB;YACzClB,MAAM,EAAEO,MAAM,CAACP;EACzB,SAAS,CAAC;EACV,OAAO,CAAC;EACJxD,MAAAA,QAAQ,CAAC5B,IAAI,CAACiP,WAAW,CAAC;EAC9B,KAAG,CAAC;EACF,IAAA,OAAOhQ,OAAO,CAAC0C,GAAG,CAACC,QAAQ,CAAC;KAC7B;;EAED;EACA;EACA;EACA;EACAiL,EAAAA,IAAI,CAACrL,SAAS,CAAC0N,KAAK,GAAG,YAAY;EACjC,IAAA,IAAIC,YAAY,GAAG,IAAI,CAACrC,OAAO,CAAC9N,MAAM;MACtC,IAAIoQ,WAAW,GAAG,IAAI,CAACtC,OAAO,CAACuC,MAAM,CAAC,UAAU1J,MAAM,EAAE;EACtD,MAAA,OAAOA,MAAM,CAAC2F,IAAI,EAAE;OACrB,CAAC,CAACtM,MAAM;MAET,OAAO;EACLmQ,MAAAA,YAAY,EAAGA,YAAY;EAC3BC,MAAAA,WAAW,EAAIA,WAAW;QAC1BE,WAAW,EAAIH,YAAY,GAAGC,WAAW;EAEzCG,MAAAA,YAAY,EAAG,IAAI,CAACxC,KAAK,CAAC/N,MAAM;EAChCwQ,MAAAA,WAAW,EAAIJ;OAChB;KACF;;EAED;EACA;EACA;EACA;EACAvC,EAAAA,IAAI,CAACrL,SAAS,CAACqM,iBAAiB,GAAG,YAAW;MAC5C,IAAI,IAAI,CAACF,UAAU,EAAE;EACnB,MAAA,KAAI,IAAI3L,CAAC,GAAG,IAAI,CAAC8K,OAAO,CAAC9N,MAAM,EAAEgD,CAAC,GAAG,IAAI,CAAC2L,UAAU,EAAE3L,CAAC,EAAE,EAAE;UACzD,IAAI,CAAC8K,OAAO,CAAC9M,IAAI,CAAC,IAAI,CAAC6O,oBAAoB,EAAE,CAAC;EAC/C;EACF;KACF;;EAED;EACA;EACA;EACA;EACA;EACAhC,EAAAA,IAAI,CAACrL,SAAS,CAACqN,oBAAoB,GAAG,YAAY;EAChD,IAAA,IAAMY,gBAAgB,GAAG,IAAI,CAACpC,cAAc,CAAC;QAC3ClG,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBF,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvB1B,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3Be,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;QACvClB,MAAM,EAAE,IAAI,CAACA;OACd,CAAC,IAAI,EAAE;MAER,OAAO,IAAI2D,aAAa,CAAC0G,gBAAgB,CAACrK,MAAM,IAAI,IAAI,CAACA,MAAM,EAAE;EAC/D+B,MAAAA,QAAQ,EAAEsI,gBAAgB,CAACtI,QAAQ,IAAI,IAAI,CAACA,QAAQ;EACpDF,MAAAA,QAAQ,EAAEwI,gBAAgB,CAACxI,QAAQ,IAAI,IAAI,CAACA,QAAQ;EACpD1B,MAAAA,UAAU,EAAEkK,gBAAgB,CAAClK,UAAU,IAAI,IAAI,CAACA,UAAU;EAC1De,MAAAA,gBAAgB,EAAEmJ,gBAAgB,CAACnJ,gBAAgB,IAAI,IAAI,CAACA,gBAAgB;QAC5EuB,SAAS,EAAE+E,oBAAoB,CAACJ,uBAAuB,CAAC,IAAI,CAACS,cAAc,CAAC;QAC5E5H,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B4D,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;QACnDxC,cAAc,EAAE,IAAI,CAACA;EACzB,KAAG,CAAC;KACH;;EAED;EACA;EACA;EACA;EACA;IACA,SAAS8G,kBAAkBA,CAACC,UAAU,EAAE;EACtC,IAAA,IAAI,CAACkC,QAAQ,CAAClC,UAAU,CAAC,IAAI,CAACmC,SAAS,CAACnC,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;EACrE,MAAA,MAAM,IAAIW,SAAS,CAAC,kDAAkD,CAAC;EACxE;EACH;;EAEA;EACA;EACA;EACA;EACA;IACA,SAASP,kBAAkBA,CAACD,UAAU,EAAE;EACtC,IAAA,IAAI,CAAC+B,QAAQ,CAAC/B,UAAU,CAAC,IAAI,CAACgC,SAAS,CAAChC,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;EACrE,MAAA,MAAM,IAAIQ,SAAS,CAAC,kDAAkD,CAAC;EACxE;EACH;;EAEA;EACA;EACA;EACA;EACA;IACA,SAASuB,QAAQA,CAACnF,KAAK,EAAE;MACvB,OAAO,OAAOA,KAAK,KAAK,QAAQ;EAClC;;EAEA;EACA;EACA;EACA;EACA;IACA,SAASoF,SAASA,CAACpF,KAAK,EAAE;EACxB,IAAA,OAAOkD,IAAI,CAACmC,KAAK,CAACrF,KAAK,CAAC,IAAIA,KAAK;EACnC;EAEAsF,EAAAA,MAAc,GAAGhD,IAAI;;;;;;;;;;;;;;;;;ECrdrB,EAAA,SAASiD,QAAQA,CAAC1N,OAAO,EAAE8D,QAAQ,EAAE;MACnC,IAAI,CAAC9D,OAAO,GAAGA,OAAO;MACtB,IAAI,CAAC8D,QAAQ,GAAGA,QAAQ;EAC1B;EAEAA,EAAAA,QAAc,GAAG4J,QAAQ;;;;;;;;;ECNzB,IAAA,IAAIA,QAAQ,GAAGlM,eAAA,EAAqB;;EAEpC;EACA;EACA;EACA,IAAA,IAAI3E,OAAO,GAAG6E,eAAoB,EAAA,CAAC7E,OAAO;EAC1C;EACA;EACA;EACA;MACA,IAAIgF,mBAAmB,GAAG,0BAA0B;;EAEpD;EACA;EACA;EACA;MACA,IAAIC,iBAAiB,GAAG,wBAAwB;EAChD;;MAGA,IAAI6L,eAAe,GAAG,IAAK;;EAE3B;EACA;EACA,IAAA,IAAIpK,MAAM,GAAG;EACXqK,MAAAA,IAAI,EAAE,SAANA,IAAIA,GAAa;OAClB;;EAED;EACA;EACA,IAAA,IAAIC,YAAY,GAAG;EACnB;EACA;EACA;EACA;EACA;EACEC,MAAAA,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAWC,QAAQ,EAAE;EACnCxK,QAAAA,MAAM,CAACyK,cAAc,CAACpQ,IAAI,CAACmQ,QAAQ,CAAC;SACrC;EAEH;EACA;EACA;EACA;QACEpJ,IAAI,EAAEpB,MAAM,CAACoB;OACd;EAED,IAAA,IAAI,OAAOlI,IAAI,KAAK,WAAW,IAAI,OAAOsH,WAAW,KAAK,UAAU,IAAI,OAAOJ,gBAAgB,KAAK,UAAU,EAAE;EAChH;EACEJ,MAAAA,MAAM,CAACE,EAAE,GAAG,UAAUC,KAAK,EAAExE,QAAQ,EAAE;EACrCyE,QAAAA,gBAAgB,CAACD,KAAK,EAAE,UAAU1D,OAAO,EAAE;EACzCd,UAAAA,QAAQ,CAACc,OAAO,CAAC4D,IAAI,CAAC;EAC5B,SAAK,CAAC;SACH;EACDL,MAAAA,MAAM,CAACM,IAAI,GAAG,UAAU7D,OAAO,EAAE8D,QAAQ,EAAE;UACxCA,QAAQ,GAAGC,WAAW,CAAC/D,OAAO,EAAE8D,QAAQ,CAAC,GAAGC,WAAW,CAAE/D,OAAO,CAAC;SACnE;EACH,KAAC,MACI,IAAI,OAAO9D,OAAO,KAAK,WAAW,EAAE;EACzC;;EAEE,MAAA,IAAI8F,aAAa;QACjB,IAAI;EACFA,QAAAA,aAAa,GAAG5F,OAAQ,CAAA,gBAAgB,CAAC;SAC1C,CAAC,OAAMoC,KAAK,EAAE;EACb,QAAA,IAAI4D,OAAA,CAAO5D,KAAK,CAAA,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAAC6D,IAAI,KAAK,kBAAkB,EAAE,CAErF,MAAM;EACL,UAAA,MAAM7D,KAAK;EACZ;EACF;EAED,MAAA,IAAIwD,aAAa;EAEfA,MAAAA,aAAa,CAACiM,UAAU,KAAK,IAAI,EAAE;EACnC,QAAA,IAAIA,UAAU,GAAIjM,aAAa,CAACiM,UAAU;UAC1C1K,MAAM,CAACM,IAAI,GAAGoK,UAAU,CAAClK,WAAW,CAAC6H,IAAI,CAACqC,UAAU,CAAC;UACrD1K,MAAM,CAACE,EAAE,GAAGwK,UAAU,CAACxK,EAAE,CAACmI,IAAI,CAACqC,UAAU,CAAC;UAC1C1K,MAAM,CAACqK,IAAI,GAAG1R,OAAO,CAAC0R,IAAI,CAAChC,IAAI,CAAC1P,OAAO,CAAC;EAC5C,OAAG,MAAM;UACLqH,MAAM,CAACE,EAAE,GAAGvH,OAAO,CAACuH,EAAE,CAACmI,IAAI,CAAC1P,OAAO,CAAC;EACxC;EACIqH,QAAAA,MAAM,CAACM,IAAI,GAAG,UAAU7D,OAAO,EAAE;EAC/B9D,UAAAA,OAAO,CAAC2H,IAAI,CAAC7D,OAAO,CAAC;WACtB;EACL;EACIuD,QAAAA,MAAM,CAACE,EAAE,CAAC,YAAY,EAAE,YAAY;EAClCvH,UAAAA,OAAO,CAAC0R,IAAI,CAAC,CAAC,CAAC;EACrB,SAAK,CAAC;UACFrK,MAAM,CAACqK,IAAI,GAAG1R,OAAO,CAAC0R,IAAI,CAAChC,IAAI,CAAC1P,OAAO,CAAC;EACzC;EACH,KAAC,MACI;EACH,MAAA,MAAM,IAAIgE,KAAK,CAAC,qCAAqC,CAAC;EACxD;MAEA,SAASgO,YAAYA,CAAC1P,KAAK,EAAE;EAC3B,MAAA,OAAOmC,MAAM,CAACwN,mBAAmB,CAAC3P,KAAK,CAAC,CAAC4P,MAAM,CAAC,UAASC,OAAO,EAAEjO,IAAI,EAAE;EACtE,QAAA,OAAOO,MAAM,CAAC2N,cAAc,CAACD,OAAO,EAAEjO,IAAI,EAAE;EAC/C+H,UAAAA,KAAK,EAAE3J,KAAK,CAAC4B,IAAI,CAAC;EAClBmO,UAAAA,UAAU,EAAE;EACb,SAAK,CAAC;SACH,EAAE,EAAE,CAAC;EACR;;EAEA;EACA;EACA;EACA;EACA;EACA;MACA,SAASC,SAASA,CAACrG,KAAK,EAAE;EACxB,MAAA,OAAOA,KAAK,IAAK,OAAOA,KAAK,CAACtK,IAAI,KAAK,UAAW,IAAK,OAAOsK,KAAK,CAACa,KAAK,KAAK,UAAW;EAC3F;;EAEA;EACAzF,IAAAA,MAAM,CAACsF,OAAO,GAAG,EAAE;;EAEnB;EACA;EACA;EACA;EACA;EACA;MACAtF,MAAM,CAACsF,OAAO,CAAC4F,GAAG,GAAG,SAASA,GAAGA,CAACnQ,EAAE,EAAEoQ,IAAI,EAAE;QAC1C,IAAIxQ,CAAC,GAAG,IAAIyQ,QAAQ,CAAC,UAAU,GAAGrQ,EAAE,GAAG,2BAA2B,CAAC;QACnEJ,CAAC,CAACqF,MAAM,GAAGsK,YAAY;EACvB,MAAA,OAAO3P,CAAC,CAAC0Q,KAAK,CAAC1Q,CAAC,EAAEwQ,IAAI,CAAC;OACxB;;EAED;EACA;EACA;EACA;MACAnL,MAAM,CAACsF,OAAO,CAACA,OAAO,GAAG,SAASA,OAAOA,GAAG;EAC1C,MAAA,OAAOlI,MAAM,CAACC,IAAI,CAAC2C,MAAM,CAACsF,OAAO,CAAC;OACnC;;EAED;EACA;EACA;MACAtF,MAAM,CAACoF,kBAAkB,GAAG7C,SAAS;MAErCvC,MAAM,CAACsL,oBAAoB,GAAGlB,eAAe;;EAE7C;EACA;EACA;EACA;MACApK,MAAM,CAACyK,cAAc,GAAG,EAAE;;EAE1B;EACA;EACA;EACA;EACA;EACAzK,IAAAA,MAAM,CAACuL,gBAAgB,GAAG,UAASzM,IAAI,EAAE;EACvC,MAAA,IAAI0M,KAAK,GAAG,SAARA,KAAKA,GAAc;EACrBxL,QAAAA,MAAM,CAACqK,IAAI,CAACvL,IAAI,CAAC;SAClB;EAED,MAAA,IAAG,CAACkB,MAAM,CAACoF,kBAAkB,EAAE;UAC7B,OAAOoG,KAAK,EAAE;EACf;EAED,MAAA,IAAI3Q,MAAM,GAAGmF,MAAM,CAACoF,kBAAkB,CAACtG,IAAI,CAAC;EAC5C,MAAA,IAAImM,SAAS,CAACpQ,MAAM,CAAC,EAAE;EACrBA,QAAAA,MAAM,CAACP,IAAI,CAACkR,KAAK,EAAEA,KAAK,CAAC;EAEzB,QAAA,OAAO3Q,MAAM;EACjB,OAAG,MAAM;EACL2Q,QAAAA,KAAK,EAAE;EACP,QAAA,OAAO,IAAIlS,OAAO,CAAC,UAAUsB,QAAQ,EAAEJ,MAAM,EAAE;EAC7CA,UAAAA,MAAM,CAAC,IAAImC,KAAK,CAAC,oBAAoB,CAAC,CAAC;EAC7C,SAAK,CAAC;EACH;OACF;;EAID;EACA;EACA;EACA;EACA;EACAqD,IAAAA,MAAM,CAAC+F,OAAO,GAAG,UAAS0F,SAAS,EAAE;EAEnC,MAAA,IAAI,CAACzL,MAAM,CAACyK,cAAc,CAACpR,MAAM,EAAE;UACjC2G,MAAM,CAACM,IAAI,CAAC;EACVuD,UAAAA,EAAE,EAAE4H,SAAS;EACbzH,UAAAA,MAAM,EAAEzF,iBAAiB;EACzBtD,UAAAA,KAAK,EAAE0P,YAAY,CAAC,IAAIhO,KAAK,CAAC,oBAAoB,CAAC;EACzD,SAAK,CAAC;;EAEN;EACA;EACI,QAAA,OAAO,IAAIrD,OAAO,CAAC,UAASiB,OAAO,EAAE;EAAEA,UAAAA,OAAO,EAAE;EAAC,SAAE,CAAC;EACrD;EAGD,MAAA,IAAIiR,KAAK,GAAG,SAARA,KAAKA,GAAc;UACrBxL,MAAM,CAACqK,IAAI,EAAE;SACd;EAED,MAAA,IAAIqB,MAAM,GAAG,SAATA,MAAMA,GAAc;EACtB,QAAA,IAAI,CAAC1L,MAAM,CAACyK,cAAc,CAACpR,MAAM,EAAE;YACjC2G,MAAM,CAACyK,cAAc,GAAG,EAAE;EAC3B;SACF;QAED,IAAMxO,QAAQ,GAAG+D,MAAM,CAACyK,cAAc,CAACkB,GAAG,CAAC,UAAAnB,QAAQ,EAAA;UAAA,OAAIA,QAAQ,EAAE;SAAC,CAAA;EAClE,MAAA,IAAIoB,OAAO;QACX,IAAMC,cAAc,GAAG,IAAIvS,OAAO,CAAC,UAACsB,QAAQ,EAAEJ,MAAM,EAAK;UACvDoR,OAAO,GAAGrQ,UAAU,CAAC,YAAY;EAC/Bf,UAAAA,MAAM,CAAC,IAAImC,KAAK,CAAC,2DAA2D,CAAC,CAAC;EACpF,SAAK,EAAEqD,MAAM,CAACsL,oBAAoB,CAAC;EACnC,OAAG,CAAC;;EAEJ;QACE,IAAMQ,aAAa,GAAGxS,OAAO,CAAC0C,GAAG,CAACC,QAAQ,CAAC,CAAC3B,IAAI,CAAC,YAAW;UAC1DoB,YAAY,CAACkQ,OAAO,CAAC;EACrBF,QAAAA,MAAM,EAAE;EACZ,OAAG,EAAE,YAAW;UACZhQ,YAAY,CAACkQ,OAAO,CAAC;EACrBJ,QAAAA,KAAK,EAAE;EACX,OAAG,CAAC;;EAEJ;EACA;EACA;EACA;EACA;EACA;EACE,MAAA,OAAOlS,OAAO,CAAC0C,GAAG,CAAC,CACjB8P,aAAa,EACbD,cAAc,CACf,CAAC,CAACvR,IAAI,CAAC,YAAW;UACjB0F,MAAM,CAACM,IAAI,CAAC;EACVuD,UAAAA,EAAE,EAAE4H,SAAS;EACbzH,UAAAA,MAAM,EAAEzF,iBAAiB;EACzBtD,UAAAA,KAAK,EAAE;EACb,SAAK,CAAC;SACH,EAAE,UAAS4J,GAAG,EAAE;UACf7E,MAAM,CAACM,IAAI,CAAC;EACVuD,UAAAA,EAAE,EAAE4H,SAAS;EACbzH,UAAAA,MAAM,EAAEzF,iBAAiB;EACzBtD,UAAAA,KAAK,EAAE4J,GAAG,GAAG8F,YAAY,CAAC9F,GAAG,CAAC,GAAG;EACvC,SAAK,CAAC;EACN,OAAG,CAAC;OACH;MAED,IAAIkH,gBAAgB,GAAG,IAAI;EAE3B/L,IAAAA,MAAM,CAACE,EAAE,CAAC,SAAS,EAAE,UAAUyE,OAAO,EAAE;QACtC,IAAIA,OAAO,KAAKrG,mBAAmB,EAAE;EACnC,QAAA,OAAO0B,MAAM,CAACuL,gBAAgB,CAAC,CAAC,CAAC;EAClC;EAED,MAAA,IAAI5G,OAAO,CAACX,MAAM,KAAKzF,iBAAiB,EAAE;EACxC,QAAA,OAAOyB,MAAM,CAAC+F,OAAO,CAACpB,OAAO,CAACd,EAAE,CAAC;EAClC;QAED,IAAI;UACF,IAAIG,MAAM,GAAGhE,MAAM,CAACsF,OAAO,CAACX,OAAO,CAACX,MAAM,CAAC;EAE3C,QAAA,IAAIA,MAAM,EAAE;YACV+H,gBAAgB,GAAGpH,OAAO,CAACd,EAAE;;EAEnC;YACM,IAAIhJ,MAAM,GAAGmJ,MAAM,CAACqH,KAAK,CAACrH,MAAM,EAAEW,OAAO,CAACa,MAAM,CAAC;EAEjD,UAAA,IAAIyF,SAAS,CAACpQ,MAAM,CAAC,EAAE;EAC7B;EACQA,YAAAA,MAAM,CACDP,IAAI,CAAC,UAAUO,MAAM,EAAE;gBACtB,IAAIA,MAAM,YAAYsP,QAAQ,EAAE;kBAC9BnK,MAAM,CAACM,IAAI,CAAC;oBACVuD,EAAE,EAAEc,OAAO,CAACd,EAAE;oBACdhJ,MAAM,EAAEA,MAAM,CAAC4B,OAAO;EACtBxB,kBAAAA,KAAK,EAAE;EACzB,iBAAiB,EAAEJ,MAAM,CAAC0F,QAAQ,CAAC;EACnC,eAAe,MAAM;kBACLP,MAAM,CAACM,IAAI,CAAC;oBACVuD,EAAE,EAAEc,OAAO,CAACd,EAAE;EACdhJ,kBAAAA,MAAM,EAAEA,MAAM;EACdI,kBAAAA,KAAK,EAAE;EACzB,iBAAiB,CAAC;EACH;EACD8Q,cAAAA,gBAAgB,GAAG,IAAI;EACrC,aAAa,CAAC,CACDtG,KAAK,CAAC,UAAUZ,GAAG,EAAE;gBACpB7E,MAAM,CAACM,IAAI,CAAC;kBACVuD,EAAE,EAAEc,OAAO,CAACd,EAAE;EACdhJ,gBAAAA,MAAM,EAAE,IAAI;kBACZI,KAAK,EAAE0P,YAAY,CAAC9F,GAAG;EACvC,eAAe,CAAC;EACFkH,cAAAA,gBAAgB,GAAG,IAAI;EACrC,aAAa,CAAC;EACP,WAAA,MACI;EACX;cACQ,IAAIlR,MAAM,YAAYsP,QAAQ,EAAE;gBAC9BnK,MAAM,CAACM,IAAI,CAAC;kBACVuD,EAAE,EAAEc,OAAO,CAACd,EAAE;kBACdhJ,MAAM,EAAEA,MAAM,CAAC4B,OAAO;EACtBxB,gBAAAA,KAAK,EAAE;EACnB,eAAW,EAAEJ,MAAM,CAAC0F,QAAQ,CAAC;EAC7B,aAAS,MAAM;gBACLP,MAAM,CAACM,IAAI,CAAC;kBACVuD,EAAE,EAAEc,OAAO,CAACd,EAAE;EACdhJ,gBAAAA,MAAM,EAAEA,MAAM;EACdI,gBAAAA,KAAK,EAAE;EACnB,eAAW,CAAC;EACH;EAED8Q,YAAAA,gBAAgB,GAAG,IAAI;EACxB;EACF,SAAA,MACI;YACH,MAAM,IAAIpP,KAAK,CAAC,kBAAkB,GAAGgI,OAAO,CAACX,MAAM,GAAG,GAAG,CAAC;EAC3D;SACF,CACD,OAAOa,GAAG,EAAE;UACV7E,MAAM,CAACM,IAAI,CAAC;YACVuD,EAAE,EAAEc,OAAO,CAACd,EAAE;EACdhJ,UAAAA,MAAM,EAAE,IAAI;YACZI,KAAK,EAAE0P,YAAY,CAAC9F,GAAG;EAC7B,SAAK,CAAC;EACH;EACH,KAAC,CAAC;;EAEF;EACA;EACA;EACA;EACA;EACA7E,IAAAA,MAAM,CAACgM,QAAQ,GAAG,UAAU1G,OAAO,EAAEtI,OAAO,EAAE;EAE5C,MAAA,IAAIsI,OAAO,EAAE;EACX,QAAA,KAAK,IAAIzI,IAAI,IAAIyI,OAAO,EAAE;EACxB,UAAA,IAAIA,OAAO,CAAC2G,cAAc,CAACpP,IAAI,CAAC,EAAE;cAChCmD,MAAM,CAACsF,OAAO,CAACzI,IAAI,CAAC,GAAGyI,OAAO,CAACzI,IAAI,CAAC;cACpCmD,MAAM,CAACsF,OAAO,CAACzI,IAAI,CAAC,CAACmD,MAAM,GAAGsK,YAAY;EAC3C;EACF;EACF;EAED,MAAA,IAAItN,OAAO,EAAE;EACXgD,QAAAA,MAAM,CAACoF,kBAAkB,GAAGpI,OAAO,CAACkP,WAAW;EACnD;EACIlM,QAAAA,MAAM,CAACsL,oBAAoB,GAAGtO,OAAO,CAACsO,oBAAoB,IAAIlB,eAAe;EAC9E;EAEDpK,MAAAA,MAAM,CAACM,IAAI,CAAC,OAAO,CAAC;OACrB;EAEDN,IAAAA,MAAM,CAACoB,IAAI,GAAG,UAAUyB,OAAO,EAAE;EAC/B,MAAA,IAAIkJ,gBAAgB,EAAE;UACpB,IAAIlJ,OAAO,YAAYsH,QAAQ,EAAE;YAC/BnK,MAAM,CAACM,IAAI,CAAC;EACVuD,YAAAA,EAAE,EAAEkI,gBAAgB;EACpBjI,YAAAA,OAAO,EAAE,IAAI;cACbjB,OAAO,EAAEA,OAAO,CAACpG;EACzB,WAAO,EAAEoG,OAAO,CAACtC,QAAQ,CAAC;EACpB,UAAA;EACD;UAEDP,MAAM,CAACM,IAAI,CAAC;EACVuD,UAAAA,EAAE,EAAEkI,gBAAgB;EACpBjI,UAAAA,OAAO,EAAE,IAAI;EACbjB,UAAAA,OAAO,EAAPA;EACN,SAAK,CAAC;EACH;OACF;MAGmC;EAClCpK,MAAAA,OAAc,CAAA0T,GAAA,GAAAnM,MAAM,CAACgM,QAAQ;EAC7BvT,MAAAA,OAAe,CAAA2I,IAAA,GAAApB,MAAM,CAACoB,IAAI;EAC5B;;;;;EChYA,IAAO1I,QAAQ,GAAwBuF,kBAAwB,CAAxDvF,QAAQ;IAAEI,YAAY,GAAUmF,kBAAwB,CAA9CnF,YAAY;IAAEG,IAAI,GAAIgF,kBAAwB,CAAhChF,IAAI;;EAEnC;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS4P,IAAIA,CAACpJ,MAAM,EAAEzC,OAAO,EAAE;EAC7B,EAAA,IAAIkK,IAAI,GAAG/I,WAAA,EAAiB;EAE5B,EAAA,OAAO,IAAI+I,IAAI,CAACzH,MAAM,EAAEzC,OAAO,CAAC;EAClC;AACA,MAAYoP,MAAA,GAAAC,GAAA,CAAAxD,IAAA,GAAGA;;EAEf;EACA;EACA;EACA;EACA;EACA,SAAS7I,MAAMA,CAACsF,OAAO,EAAEtI,OAAO,EAAE;EAChC,EAAA,IAAIgD,MAAM,GAAG3B,aAAA,EAAmB;EAChC2B,EAAAA,MAAM,CAACmM,GAAG,CAAC7G,OAAO,EAAEtI,OAAO,CAAC;EAC9B;AACA,MAAcsP,QAAA,GAAAD,GAAA,CAAArM,MAAA,GAAGA;;EAEjB;EACA;EACA;EACA;EACA,SAASuM,UAAUA,CAAC1J,OAAO,EAAE;EAC3B,EAAA,IAAI7C,MAAM,GAAG3B,aAAA,EAAmB;EAChC2B,EAAAA,MAAM,CAACoB,IAAI,CAACyB,OAAO,CAAC;EACtB;AACA,MAAkB2J,YAAA,GAAAH,GAAA,CAAAE,UAAA,GAAGA;EAErB,IAAAvO,UAAA,GAAkBqB,iBAAoB;IAA/B/F,SAAO,GAAA0E,UAAA,CAAP1E,OAAO;AACd,MAAewD,QAAA,GAAAuP,GAAA,CAAA/S,OAAA,GAAGA;AAEF6Q,MAAAA,QAAA,GAAAkC,GAAA,CAAAlC,QAAA,GAAGsC;AAEnB,MAAgBC,UAAA,GAAAL,GAAA,CAAA3T,QAAA,GAAGA;AACnB,MAAoBiU,cAAA,GAAAN,GAAA,CAAAvT,YAAA,GAAGA;AACvB,MAAA8T,MAAA,GAAAP,GAAA,CAAApT,IAAY,GAAGA;;;;;;;;;;;;;;;;;;"}