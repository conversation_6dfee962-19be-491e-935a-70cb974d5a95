/*! For license information please see workerpool.min.js.LICENSE.txt */
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).workerpool={})}(this,(function(e){"use strict";var r={},t={exports:{}};!function(e){var r=function(e){return void 0!==e&&null!=e.versions&&null!=e.versions.node&&e+""=="[object process]"};e.exports.isNode=r,e.exports.platform="undefined"!=typeof process&&r(process)?"node":"browser";var t="node"===e.exports.platform&&require("worker_threads");e.exports.isMainThread="node"===e.exports.platform?(!t||t.isMainThread)&&!process.connected:"undefined"!=typeof Window,e.exports.cpus="browser"===e.exports.platform?self.navigator.hardwareConcurrency:require("os").cpus().length}(t);var n,o=t.exports,i={};function s(){if(n)return i;function e(n,i){var s=this;if(!(this instanceof e))throw new SyntaxError("Constructor must be called with the new operator");if("function"!=typeof n)throw new SyntaxError("Function parameter handler(resolve, reject) missing");var u=[],a=[];this.resolved=!1,this.rejected=!1,this.pending=!0,this[Symbol.toStringTag]="Promise";var c=function(e,r){u.push(e),a.push(r)};this.then=function(t,n){return new e((function(e,o){var i=t?r(t,e,o):e,s=n?r(n,e,o):o;c(i,s)}),s)};var f=function(e){return s.resolved=!0,s.rejected=!1,s.pending=!1,u.forEach((function(r){r(e)})),c=function(r,t){r(e)},f=d=function(){},s},d=function(e){return s.resolved=!1,s.rejected=!0,s.pending=!1,a.forEach((function(r){r(e)})),c=function(r,t){t(e)},f=d=function(){},s};this.cancel=function(){return i?i.cancel():d(new t),s},this.timeout=function(e){if(i)i.timeout(e);else{var r=setTimeout((function(){d(new o("Promise timed out after "+e+" ms"))}),e);s.always((function(){clearTimeout(r)}))}return s},n((function(e){f(e)}),(function(e){d(e)}))}function r(e,r,t){return function(n){try{var o=e(n);o&&"function"==typeof o.then&&"function"==typeof o.catch?o.then(r,t):r(o)}catch(e){t(e)}}}function t(e){this.message=e||"promise cancelled",this.stack=(new Error).stack}function o(e){this.message=e||"timeout exceeded",this.stack=(new Error).stack}return n=1,e.prototype.catch=function(e){return this.then(null,e)},e.prototype.always=function(e){return this.then(e,e)},e.prototype.finally=function(r){var t=this,n=function(){return new e((function(e){return e()})).then(r).then((function(){return t}))};return this.then(n,n)},e.all=function(r){return new e((function(e,t){var n=r.length,o=[];n?r.forEach((function(r,i){r.then((function(r){o[i]=r,0==--n&&e(o)}),(function(e){n=0,t(e)}))})):e(o)}))},e.defer=function(){var r={};return r.promise=new e((function(e,t){r.resolve=e,r.reject=t})),r},t.prototype=new Error,t.prototype.constructor=Error,t.prototype.name="CancellationError",e.CancellationError=t,o.prototype=new Error,o.prototype.constructor=Error,o.prototype.name="TimeoutError",e.TimeoutError=o,i.Promise=e,i}function u(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function a(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,r){if(e){if("string"==typeof e)return u(e,r);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?u(e,r):void 0}}(e))||r){t&&(e=t);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return s=e.done,e},e:function(e){a=!0,i=e},f:function(){try{s||null==t.return||t.return()}finally{if(a)throw i}}}}function c(e,r,t){return(r=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function f(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,n)}return t}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}var p,l,h,m,k,w,y,v,g={exports:{}},b={};function O(){return p||(p=1,b.validateOptions=function(e,r,t){if(e){var n=e?Object.keys(e):[],o=n.find((function(e){return!r.includes(e)}));if(o)throw new Error('Object "'+t+'" contains an unknown option "'+o+'"');var i=r.find((function(e){return Object.prototype[e]&&!n.includes(e)}));if(i)throw new Error('Object "'+t+'" contains an inherited option "'+i+'" which is not defined in the object itself but in its prototype. Only plain objects are allowed. Please remove the option from the prototype or override it with a value "undefined".');return e}},b.workerOptsNames=["credentials","name","type"],b.forkOptsNames=["cwd","detached","env","execPath","execArgv","gid","serialization","signal","killSignal","silent","stdio","uid","windowsVerbatimArguments","timeout"],b.workerThreadOptsNames=["argv","env","eval","execArgv","stdin","stdout","stderr","workerData","trackUnmanagedFds","transferList","resourceLimits","name"]),b}function T(){if(m)return g.exports;m=1;var e=s().Promise,r=o,t=O(),n=t.validateOptions,i=t.forkOptsNames,u=t.workerThreadOptsNames,p=t.workerOptsNames,k="__workerpool-terminate__",w="__workerpool-cleanup__";function y(){var e=b();if(!e)throw new Error("WorkerPool: workerType = 'thread' is not supported, Node >= 11.7.0 required");return e}function v(){if("function"!=typeof Worker&&("object"!==("undefined"==typeof Worker?"undefined":d(Worker))||"function"!=typeof Worker.prototype.constructor))throw new Error("WorkerPool: Web Workers not supported")}function b(){try{return require("worker_threads")}catch(e){if("object"===d(e)&&null!==e&&"MODULE_NOT_FOUND"===e.code)return null;throw e}}function T(e,r,t){n(r,p,"workerOpts");var o=new t(e,r);return o.isBrowserWorker=!0,o.on=function(e,r){this.addEventListener(e,(function(e){r(e.data)}))},o.send=function(e,r){this.postMessage(e,r)},o}function x(e,r,t){var o,i;n(null==t?void 0:t.workerThreadOpts,u,"workerThreadOpts");var s=new r.Worker(e,function(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?f(Object(t),!0).forEach((function(r){c(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):f(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}({stdout:null!==(o=null==t?void 0:t.emitStdStreams)&&void 0!==o&&o,stderr:null!==(i=null==t?void 0:t.emitStdStreams)&&void 0!==i&&i},null==t?void 0:t.workerThreadOpts));return s.isWorkerThread=!0,s.send=function(e,r){this.postMessage(e,r)},s.kill=function(){return this.terminate(),!0},s.disconnect=function(){this.terminate()},null!=t&&t.emitStdStreams&&(s.stdout.on("data",(function(e){return s.emit("stdout",e)})),s.stderr.on("data",(function(e){return s.emit("stderr",e)}))),s}function E(e,r,t){n(r.forkOpts,i,"forkOpts");var o=t.fork(e,r.forkArgs,r.forkOpts),s=o.send;return o.send=function(e){return s.call(o,e)},r.emitStdStreams&&(o.stdout.on("data",(function(e){return o.emit("stdout",e)})),o.stderr.on("data",(function(e){return o.emit("stderr",e)}))),o.isChildProcess=!0,o}function j(e){e=e||{};var r=process.execArgv.join(" "),t=-1!==r.indexOf("--inspect"),n=-1!==r.indexOf("--debug-brk"),o=[];return t&&(o.push("--inspect="+e.debugPort),n&&o.push("--debug-brk")),process.execArgv.forEach((function(e){e.indexOf("--max-old-space-size")>-1&&o.push(e)})),Object.assign({},e,{forkArgs:e.forkArgs,forkOpts:Object.assign({},e.forkOpts,{execArgv:(e.forkOpts&&e.forkOpts.execArgv||[]).concat(o),stdio:e.emitStdStreams?"pipe":void 0})})}function W(e){for(var r=new Error(""),t=Object.keys(e),n=0;n<t.length;n++)r[t[n]]=e[t[n]];return r}function _(e,r){Object.values(e.processing).forEach((function(e){var t;return null==e||null===(t=e.options)||void 0===t?void 0:t.on(r)})),Object.values(e.tracking).forEach((function(e){var t;return null==e||null===(t=e.options)||void 0===t?void 0:t.on(r)}))}function S(e,t){var n=this,o=t||{};function i(e){for(var r in n.terminated=!0,n.processing)void 0!==n.processing[r]&&n.processing[r].resolver.reject(e);n.processing=Object.create(null)}this.script=e||function(){if("browser"===r.platform){if("undefined"==typeof Blob)throw new Error("Blob not supported by the browser");if(!window.URL||"function"!=typeof window.URL.createObjectURL)throw new Error("URL.createObjectURL not supported by the browser");var e=new Blob([h?l:(h=1,l='!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(e="undefined"!=typeof globalThis?globalThis:e||self).worker=n()}(this,(function(){"use strict";function e(n){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(n)}function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t={};var r=function(e,n){this.message=e,this.transfer=n},o={};function i(e,n){var t=this;if(!(this instanceof i))throw new SyntaxError("Constructor must be called with the new operator");if("function"!=typeof e)throw new SyntaxError("Function parameter handler(resolve, reject) missing");var r=[],o=[];this.resolved=!1,this.rejected=!1,this.pending=!0,this[Symbol.toStringTag]="Promise";var a=function(e,n){r.push(e),o.push(n)};this.then=function(e,n){return new i((function(t,r){var o=e?u(e,t,r):t,i=n?u(n,t,r):r;a(o,i)}),t)};var f=function(e){return t.resolved=!0,t.rejected=!1,t.pending=!1,r.forEach((function(n){n(e)})),a=function(n,t){n(e)},f=d=function(){},t},d=function(e){return t.resolved=!1,t.rejected=!0,t.pending=!1,o.forEach((function(n){n(e)})),a=function(n,t){t(e)},f=d=function(){},t};this.cancel=function(){return n?n.cancel():d(new s),t},this.timeout=function(e){if(n)n.timeout(e);else{var r=setTimeout((function(){d(new c("Promise timed out after "+e+" ms"))}),e);t.always((function(){clearTimeout(r)}))}return t},e((function(e){f(e)}),(function(e){d(e)}))}function u(e,n,t){return function(r){try{var o=e(r);o&&"function"==typeof o.then&&"function"==typeof o.catch?o.then(n,t):n(o)}catch(e){t(e)}}}function s(e){this.message=e||"promise cancelled",this.stack=(new Error).stack}function c(e){this.message=e||"timeout exceeded",this.stack=(new Error).stack}return i.prototype.catch=function(e){return this.then(null,e)},i.prototype.always=function(e){return this.then(e,e)},i.prototype.finally=function(e){var n=this,t=function(){return new i((function(e){return e()})).then(e).then((function(){return n}))};return this.then(t,t)},i.all=function(e){return new i((function(n,t){var r=e.length,o=[];r?e.forEach((function(e,i){e.then((function(e){o[i]=e,0==--r&&n(o)}),(function(e){r=0,t(e)}))})):n(o)}))},i.defer=function(){var e={};return e.promise=new i((function(n,t){e.resolve=n,e.reject=t})),e},s.prototype=new Error,s.prototype.constructor=Error,s.prototype.name="CancellationError",i.CancellationError=s,c.prototype=new Error,c.prototype.constructor=Error,c.prototype.name="TimeoutError",i.TimeoutError=c,o.Promise=i,function(n){var t=r,i=o.Promise,u="__workerpool-cleanup__",s={exit:function(){}},c={addAbortListener:function(e){s.abortListeners.push(e)},emit:s.emit};if("undefined"!=typeof self&&"function"==typeof postMessage&&"function"==typeof addEventListener)s.on=function(e,n){addEventListener(e,(function(e){n(e.data)}))},s.send=function(e,n){n?postMessage(e,n):postMessage(e)};else{if("undefined"==typeof process)throw new Error("Script must be executed as a worker");var a;try{a=require("worker_threads")}catch(n){if("object"!==e(n)||null===n||"MODULE_NOT_FOUND"!==n.code)throw n}if(a&&null!==a.parentPort){var f=a.parentPort;s.send=f.postMessage.bind(f),s.on=f.on.bind(f),s.exit=process.exit.bind(process)}else s.on=process.on.bind(process),s.send=function(e){process.send(e)},s.on("disconnect",(function(){process.exit(1)})),s.exit=process.exit.bind(process)}function d(e){return Object.getOwnPropertyNames(e).reduce((function(n,t){return Object.defineProperty(n,t,{value:e[t],enumerable:!0})}),{})}function l(e){return e&&"function"==typeof e.then&&"function"==typeof e.catch}s.methods={},s.methods.run=function(e,n){var t=new Function("return ("+e+").apply(this, arguments);");return t.worker=c,t.apply(t,n)},s.methods.methods=function(){return Object.keys(s.methods)},s.terminationHandler=void 0,s.abortListenerTimeout=1e3,s.abortListeners=[],s.terminateAndExit=function(e){var n=function(){s.exit(e)};if(!s.terminationHandler)return n();var t=s.terminationHandler(e);return l(t)?(t.then(n,n),t):(n(),new i((function(e,n){n(new Error("Worker terminating"))})))},s.cleanup=function(e){if(!s.abortListeners.length)return s.send({id:e,method:u,error:d(new Error("Worker terminating"))}),new i((function(e){e()}));var n,t=s.abortListeners.map((function(e){return e()})),r=new i((function(e,t){n=setTimeout((function(){t(new Error("Timeout occured waiting for abort handler, killing worker"))}),s.abortListenerTimeout)})),o=i.all(t).then((function(){clearTimeout(n),s.abortListeners.length||(s.abortListeners=[])}),(function(){clearTimeout(n),s.exit()}));return i.all([o,r]).then((function(){s.send({id:e,method:u,error:null})}),(function(n){s.send({id:e,method:u,error:n?d(n):null})}))};var p=null;s.on("message",(function(e){if("__workerpool-terminate__"===e)return s.terminateAndExit(0);if(e.method===u)return s.cleanup(e.id);try{var n=s.methods[e.method];if(!n)throw new Error(\'Unknown method "\'+e.method+\'"\');p=e.id;var r=n.apply(n,e.params);l(r)?r.then((function(n){n instanceof t?s.send({id:e.id,result:n.message,error:null},n.transfer):s.send({id:e.id,result:n,error:null}),p=null})).catch((function(n){s.send({id:e.id,result:null,error:d(n)}),p=null})):(r instanceof t?s.send({id:e.id,result:r.message,error:null},r.transfer):s.send({id:e.id,result:r,error:null}),p=null)}catch(n){s.send({id:e.id,result:null,error:d(n)})}})),s.register=function(e,n){if(e)for(var t in e)e.hasOwnProperty(t)&&(s.methods[t]=e[t],s.methods[t].worker=c);n&&(s.terminationHandler=n.onTerminate,s.abortListenerTimeout=n.abortListenerTimeout||1e3),s.send("ready")},s.emit=function(e){if(p){if(e instanceof t)return void s.send({id:p,isEvent:!0,payload:e.message},e.transfer);s.send({id:p,isEvent:!0,payload:e})}},n.add=s.register,n.emit=s.emit}(t),n(t)}));\n//# sourceMappingURL=worker.min.js.map\n')],{type:"text/javascript"});return window.URL.createObjectURL(e)}return __dirname+"/worker.js"}(),this.worker=function(e,t){if("web"===t.workerType)return v(),T(e,t.workerOpts,Worker);if("thread"===t.workerType)return x(e,n=y(),t);if("process"!==t.workerType&&t.workerType){if("browser"===r.platform)return v(),T(e,t.workerOpts,Worker);var n=b();return n?x(e,n,t):E(e,j(t),require("child_process"))}return E(e,j(t),require("child_process"))}(this.script,o),this.debugPort=o.debugPort,this.forkOpts=o.forkOpts,this.forkArgs=o.forkArgs,this.workerOpts=o.workerOpts,this.workerThreadOpts=o.workerThreadOpts,this.workerTerminateTimeout=o.workerTerminateTimeout,e||(this.worker.ready=!0),this.requestQueue=[],this.worker.on("stdout",(function(e){_(n,{stdout:e.toString()})})),this.worker.on("stderr",(function(e){_(n,{stderr:e.toString()})})),this.worker.on("message",(function(e){if(!n.terminated)if("string"==typeof e&&"ready"===e)n.worker.ready=!0,function(){var e,r=a(n.requestQueue.splice(0));try{for(r.s();!(e=r.n()).done;){var t=e.value;n.worker.send(t.message,t.transfer)}}catch(e){r.e(e)}finally{r.f()}}();else{var r,t=e.id;if(void 0!==(r=n.processing[t]))e.isEvent?r.options&&"function"==typeof r.options.on&&r.options.on(e.payload):(delete n.processing[t],!0===n.terminating&&n.terminate(),e.error?r.resolver.reject(W(e.error)):r.resolver.resolve(e.result));else void 0!==(r=n.tracking[t])&&e.isEvent&&r.options&&"function"==typeof r.options.on&&r.options.on(e.payload);if(e.method===w){var o=n.tracking[e.id];void 0!==o&&(e.error?(clearTimeout(o.timeoutId),o.resolver.reject(W(e.error))):(n.tracking&&clearTimeout(o.timeoutId),o.resolver.resolve(o.result))),delete n.tracking[t]}}}));var s=this.worker;this.worker.on("error",i),this.worker.on("exit",(function(e,r){var t="Workerpool Worker terminated Unexpectedly\n";t+="    exitCode: `"+e+"`\n",t+="    signalCode: `"+r+"`\n",t+="    workerpool.script: `"+n.script+"`\n",t+="    spawnArgs: `"+s.spawnargs+"`\n",t+="    spawnfile: `"+s.spawnfile+"`\n",t+="    stdout: `"+s.stdout+"`\n",t+="    stderr: `"+s.stderr+"`\n",i(new Error(t))})),this.processing=Object.create(null),this.tracking=Object.create(null),this.terminating=!1,this.terminated=!1,this.cleaning=!1,this.terminationHandler=null,this.lastId=0}return S.prototype.methods=function(){return this.exec("methods")},S.prototype.exec=function(r,t,n,o){n||(n=e.defer());var i=++this.lastId;this.processing[i]={id:i,resolver:n,options:o};var s={message:{id:i,method:r,params:t},transfer:o&&o.transfer};this.terminated?n.reject(new Error("Worker is terminated")):this.worker.ready?this.worker.send(s.message,s.transfer):this.requestQueue.push(s);var u=this;return n.promise.catch((function(r){if(r instanceof e.CancellationError||r instanceof e.TimeoutError)return u.tracking[i]={id:i,resolver:e.defer(),options:o},delete u.processing[i],u.tracking[i].resolver.promise=u.tracking[i].resolver.promise.catch((function(e){delete u.tracking[i];var r=u.terminateAndNotify(!0).then((function(){throw e}),(function(e){throw e}));return r})),u.worker.send({id:i,method:w}),u.tracking[i].timeoutId=setTimeout((function(){u.tracking[i].resolver.reject(r)}),u.workerTerminateTimeout),u.tracking[i].resolver.promise;throw r}))},S.prototype.busy=function(){return this.cleaning||Object.keys(this.processing).length>0},S.prototype.terminate=function(e,r){var t=this;if(e){for(var n in this.processing)void 0!==this.processing[n]&&this.processing[n].resolver.reject(new Error("Worker terminated"));this.processing=Object.create(null)}for(var o=0,i=Object.values(t.tracking);o<i.length;o++){var s=i[o];clearTimeout(s.timeoutId),s.resolver.reject(new Error("Worker Terminating"))}if(t.tracking=Object.create(null),"function"==typeof r&&(this.terminationHandler=r),this.busy())this.terminating=!0;else{var u=function(e){if(t.terminated=!0,t.cleaning=!1,null!=t.worker&&t.worker.removeAllListeners&&t.worker.removeAllListeners("message"),t.worker=null,t.terminating=!1,t.terminationHandler)t.terminationHandler(e,t);else if(e)throw e};if(this.worker){if("function"==typeof this.worker.kill){if(this.worker.killed)return void u(new Error("worker already killed!"));var a=setTimeout((function(){t.worker&&t.worker.kill()}),this.workerTerminateTimeout);return this.worker.once("exit",(function(){clearTimeout(a),t.worker&&(t.worker.killed=!0),u()})),this.worker.ready?this.worker.send(k):this.requestQueue.push({message:k}),void(this.cleaning=!0)}if("function"!=typeof this.worker.terminate)throw new Error("Failed to terminate worker");this.worker.terminate(),this.worker.killed=!0}u()}},S.prototype.terminateAndNotify=function(r,t){var n=e.defer();return t&&n.promise.timeout(t),this.terminate(r,(function(e,r){e?n.reject(e):n.resolve(r)})),n.promise},g.exports=S,g.exports._tryRequireWorkerThreads=b,g.exports._setupProcessWorker=E,g.exports._setupBrowserWorker=T,g.exports._setupWorkerThreadWorker=x,g.exports.ensureWorkerThreads=y,g.exports}function x(){if(v)return y;v=1;var e=s().Promise,r=T(),t=o,n=new(function(){if(w)return k;function e(){this.ports=Object.create(null),this.length=0}return w=1,k=e,e.prototype.nextAvailableStartingAt=function(e){for(;!0===this.ports[e];)e++;if(e>=65535)throw new Error("WorkerPool debug port limit reached: "+e+">= 65535");return this.ports[e]=!0,this.length++,e},e.prototype.releasePort=function(e){delete this.ports[e],this.length--},k}());function i(e,n){"string"==typeof e?this.script=e||null:(this.script=null,n=e),this.workers=[],this.tasks=[],n=n||{},this.forkArgs=Object.freeze(n.forkArgs||[]),this.forkOpts=Object.freeze(n.forkOpts||{}),this.workerOpts=Object.freeze(n.workerOpts||{}),this.workerThreadOpts=Object.freeze(n.workerThreadOpts||{}),this.debugPortStart=n.debugPortStart||43210,this.nodeWorker=n.nodeWorker,this.workerType=n.workerType||n.nodeWorker||"auto",this.maxQueueSize=n.maxQueueSize||1/0,this.workerTerminateTimeout=n.workerTerminateTimeout||1e3,this.onCreateWorker=n.onCreateWorker||function(){return null},this.onTerminateWorker=n.onTerminateWorker||function(){return null},this.emitStdStreams=n.emitStdStreams||!1,n&&"maxWorkers"in n?(!function(e){if(!u(e)||!a(e)||e<1)throw new TypeError("Option maxWorkers must be an integer number >= 1")}(n.maxWorkers),this.maxWorkers=n.maxWorkers):this.maxWorkers=Math.max((t.cpus||4)-1,1),n&&"minWorkers"in n&&("max"===n.minWorkers?this.minWorkers=this.maxWorkers:(!function(e){if(!u(e)||!a(e)||e<0)throw new TypeError("Option minWorkers must be an integer number >= 0")}(n.minWorkers),this.minWorkers=n.minWorkers,this.maxWorkers=Math.max(this.minWorkers,this.maxWorkers)),this._ensureMinWorkers()),this._boundNext=this._next.bind(this),"thread"===this.workerType&&r.ensureWorkerThreads()}function u(e){return"number"==typeof e}function a(e){return Math.round(e)==e}return i.prototype.exec=function(r,t,n){if(t&&!Array.isArray(t))throw new TypeError('Array expected as argument "params"');if("string"==typeof r){var o=e.defer();if(this.tasks.length>=this.maxQueueSize)throw new Error("Max queue size of "+this.maxQueueSize+" reached");var i=this.tasks,s={method:r,params:t,resolver:o,timeout:null,options:n};i.push(s);var u=o.promise.timeout;return o.promise.timeout=function(e){return-1!==i.indexOf(s)?(s.timeout=e,o.promise):u.call(o.promise,e)},this._next(),o.promise}if("function"==typeof r)return this.exec("run",[String(r),t],n);throw new TypeError('Function or string expected as argument "method"')},i.prototype.proxy=function(){if(arguments.length>0)throw new Error("No arguments expected");var e=this;return this.exec("methods").then((function(r){var t={};return r.forEach((function(r){t[r]=function(){return e.exec(r,Array.prototype.slice.call(arguments))}})),t}))},i.prototype._next=function(){if(this.tasks.length>0){var e=this._getWorker();if(e){var r=this,t=this.tasks.shift();if(t.resolver.promise.pending){var n=e.exec(t.method,t.params,t.resolver,t.options).then(r._boundNext).catch((function(){if(e.terminated)return r._removeWorker(e)})).then((function(){r._next()}));"number"==typeof t.timeout&&n.timeout(t.timeout)}else r._next()}}},i.prototype._getWorker=function(){for(var e=this.workers,r=0;r<e.length;r++){var t=e[r];if(!1===t.busy())return t}return e.length<this.maxWorkers?(t=this._createWorkerHandler(),e.push(t),t):null},i.prototype._removeWorker=function(r){var t=this;return n.releasePort(r.debugPort),this._removeWorkerFromList(r),this._ensureMinWorkers(),new e((function(e,n){r.terminate(!1,(function(o){t.onTerminateWorker({forkArgs:r.forkArgs,forkOpts:r.forkOpts,workerThreadOpts:r.workerThreadOpts,script:r.script}),o?n(o):e(r)}))}))},i.prototype._removeWorkerFromList=function(e){var r=this.workers.indexOf(e);-1!==r&&this.workers.splice(r,1)},i.prototype.terminate=function(r,t){var o=this;this.tasks.forEach((function(e){e.resolver.reject(new Error("Pool terminated"))})),this.tasks.length=0;var i=function(e){n.releasePort(e.debugPort),this._removeWorkerFromList(e)}.bind(this),s=[];return this.workers.slice().forEach((function(e){var n=e.terminateAndNotify(r,t).then(i).always((function(){o.onTerminateWorker({forkArgs:e.forkArgs,forkOpts:e.forkOpts,workerThreadOpts:e.workerThreadOpts,script:e.script})}));s.push(n)})),e.all(s)},i.prototype.stats=function(){var e=this.workers.length,r=this.workers.filter((function(e){return e.busy()})).length;return{totalWorkers:e,busyWorkers:r,idleWorkers:e-r,pendingTasks:this.tasks.length,activeTasks:r}},i.prototype._ensureMinWorkers=function(){if(this.minWorkers)for(var e=this.workers.length;e<this.minWorkers;e++)this.workers.push(this._createWorkerHandler())},i.prototype._createWorkerHandler=function(){var e=this.onCreateWorker({forkArgs:this.forkArgs,forkOpts:this.forkOpts,workerOpts:this.workerOpts,workerThreadOpts:this.workerThreadOpts,script:this.script})||{};return new r(e.script||this.script,{forkArgs:e.forkArgs||this.forkArgs,forkOpts:e.forkOpts||this.forkOpts,workerOpts:e.workerOpts||this.workerOpts,workerThreadOpts:e.workerThreadOpts||this.workerThreadOpts,debugPort:n.nextAvailableStartingAt(this.debugPortStart),workerType:this.workerType,workerTerminateTimeout:this.workerTerminateTimeout,emitStdStreams:this.emitStdStreams})},y=i}var E,j,W,_={};function S(){if(j)return E;return j=1,E=function(e,r){this.message=e,this.transfer=r}}function P(){return W||(W=1,function(e){var r=S(),t=s().Promise,n="__workerpool-cleanup__",o={exit:function(){}},i={addAbortListener:function(e){o.abortListeners.push(e)},emit:o.emit};if("undefined"!=typeof self&&"function"==typeof postMessage&&"function"==typeof addEventListener)o.on=function(e,r){addEventListener(e,(function(e){r(e.data)}))},o.send=function(e,r){r?postMessage(e,r):postMessage(e)};else{if("undefined"==typeof process)throw new Error("Script must be executed as a worker");var u;try{u=require("worker_threads")}catch(e){if("object"!==d(e)||null===e||"MODULE_NOT_FOUND"!==e.code)throw e}if(u&&null!==u.parentPort){var a=u.parentPort;o.send=a.postMessage.bind(a),o.on=a.on.bind(a),o.exit=process.exit.bind(process)}else o.on=process.on.bind(process),o.send=function(e){process.send(e)},o.on("disconnect",(function(){process.exit(1)})),o.exit=process.exit.bind(process)}function c(e){return Object.getOwnPropertyNames(e).reduce((function(r,t){return Object.defineProperty(r,t,{value:e[t],enumerable:!0})}),{})}function f(e){return e&&"function"==typeof e.then&&"function"==typeof e.catch}o.methods={},o.methods.run=function(e,r){var t=new Function("return ("+e+").apply(this, arguments);");return t.worker=i,t.apply(t,r)},o.methods.methods=function(){return Object.keys(o.methods)},o.terminationHandler=void 0,o.abortListenerTimeout=1e3,o.abortListeners=[],o.terminateAndExit=function(e){var r=function(){o.exit(e)};if(!o.terminationHandler)return r();var n=o.terminationHandler(e);return f(n)?(n.then(r,r),n):(r(),new t((function(e,r){r(new Error("Worker terminating"))})))},o.cleanup=function(e){if(!o.abortListeners.length)return o.send({id:e,method:n,error:c(new Error("Worker terminating"))}),new t((function(e){e()}));var r,i=o.abortListeners.map((function(e){return e()})),s=new t((function(e,t){r=setTimeout((function(){t(new Error("Timeout occured waiting for abort handler, killing worker"))}),o.abortListenerTimeout)})),u=t.all(i).then((function(){clearTimeout(r),o.abortListeners.length||(o.abortListeners=[])}),(function(){clearTimeout(r),o.exit()}));return t.all([u,s]).then((function(){o.send({id:e,method:n,error:null})}),(function(r){o.send({id:e,method:n,error:r?c(r):null})}))};var p=null;o.on("message",(function(e){if("__workerpool-terminate__"===e)return o.terminateAndExit(0);if(e.method===n)return o.cleanup(e.id);try{var t=o.methods[e.method];if(!t)throw new Error('Unknown method "'+e.method+'"');p=e.id;var i=t.apply(t,e.params);f(i)?i.then((function(t){t instanceof r?o.send({id:e.id,result:t.message,error:null},t.transfer):o.send({id:e.id,result:t,error:null}),p=null})).catch((function(r){o.send({id:e.id,result:null,error:c(r)}),p=null})):(i instanceof r?o.send({id:e.id,result:i.message,error:null},i.transfer):o.send({id:e.id,result:i,error:null}),p=null)}catch(r){o.send({id:e.id,result:null,error:c(r)})}})),o.register=function(e,r){if(e)for(var t in e)e.hasOwnProperty(t)&&(o.methods[t]=e[t],o.methods[t].worker=i);r&&(o.terminationHandler=r.onTerminate,o.abortListenerTimeout=r.abortListenerTimeout||1e3),o.send("ready")},o.emit=function(e){if(p){if(e instanceof r)return void o.send({id:p,isEvent:!0,payload:e.message},e.transfer);o.send({id:p,isEvent:!0,payload:e})}},e.add=o.register,e.emit=o.emit}(_)),_}var A=o.platform,L=o.isMainThread,M=o.cpus;var N=r.pool=function(e,r){return new(x())(e,r)};var U=r.worker=function(e,r){P().add(e,r)};var C=r.workerEmit=function(e){P().emit(e)},H=s().Promise,q=r.Promise=H,F=r.Transfer=S(),z=r.platform=A,D=r.isMainThread=L,I=r.cpus=M;e.Promise=q,e.Transfer=F,e.cpus=I,e.default=r,e.isMainThread=D,e.platform=z,e.pool=N,e.worker=U,e.workerEmit=C,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=workerpool.min.js.map
