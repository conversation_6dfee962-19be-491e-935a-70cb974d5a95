# Installation Guide - Augment AI Assistant

This guide will help you install and set up the Augment AI Assistant VS Code extension.

## Prerequisites

- **VS Code**: Version 1.74.0 or higher
- **API Key**: Either OpenAI or Anthropic API key
- **Node.js**: Version 16 or higher (for development)

## Installation Methods

### Method 1: Install from VSIX Package (Recommended)

1. **Download the VSIX file**: `augment-ai-assistant-0.1.0.vsix`

2. **Install via VS Code**:
   - Open VS Code
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac) to open Command Palette
   - Type "Extensions: Install from VSIX..."
   - Select the downloaded `augment-ai-assistant-0.1.0.vsix` file
   - Click "Install"

3. **Alternative installation via command line**:
   ```bash
   code --install-extension augment-ai-assistant-0.1.0.vsix
   ```

### Method 2: Install from Source (Development)

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd augment-ai-assistant
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Compile the extension**:
   ```bash
   npm run compile
   ```

4. **Launch development environment**:
   - Open the project in VS Code
   - Press `F5` to launch a new VS Code window with the extension loaded

## Configuration

### 1. Set up API Provider

1. Open VS Code Settings:
   - `File > Preferences > Settings` (or `Code > Preferences > Settings` on Mac)
   - Or press `Ctrl+,` (or `Cmd+,` on Mac)

2. Search for "Augment AI"

3. Configure the following settings:

#### For Anthropic (Recommended):
- **API Provider**: Select `anthropic`
- **API Key**: Enter your Anthropic API key (get one at https://console.anthropic.com/)
- **Model**: `claude-3-sonnet-20240229` (default)

#### For OpenAI:
- **API Provider**: Select `openai`
- **API Key**: Enter your OpenAI API key (get one at https://platform.openai.com/api-keys)
- **Model**: `gpt-4` or `gpt-3.5-turbo`

### 2. Optional Settings

- **Max Tokens**: Maximum response length (default: 4000)
- **Temperature**: Response creativity 0-2 (default: 0.1)

## Getting Started

### 1. Open the Chat Interface

- **Command Palette**: `Ctrl+Shift+P` → "Augment AI: Open Chat"
- **Sidebar**: Look for "Augment AI Chat" panel in the Explorer sidebar

### 2. First Interaction

1. Type a message in the chat input
2. Press Enter or click "Send"
3. The AI will respond with helpful information

### 3. Code Operations

#### Explain Code:
1. Select code in any file
2. Right-click → "Explain Selected Code"
3. Or use Command Palette: "Augment AI: Explain Selected Code"

#### Generate Code:
1. Use Command Palette: "Augment AI: Generate Code"
2. Describe what you want to create
3. The AI will generate code and provide options to insert it

#### Refactor Code:
1. Select code to refactor
2. Right-click → "Refactor Code"
3. Choose refactoring type or provide custom instructions

## Verification

### Test the Installation

1. **Open Chat**: Verify the chat panel appears
2. **Send Test Message**: Type "Hello" and confirm you get a response
3. **Test Code Features**: Try explaining a simple piece of code

### Troubleshooting

#### "API key is required" Error:
- Ensure you've set the API key in VS Code settings
- Verify the API key is valid and has sufficient credits

#### Extension Not Loading:
- Check VS Code version (must be 1.74.0+)
- Restart VS Code after installation
- Check the Output panel for error messages

#### Chat Not Responding:
- Verify internet connection
- Check API key validity
- Look for rate limiting messages

## Usage Tips

### Best Practices:
1. **Be Specific**: Provide clear, detailed requests
2. **Use Context**: The AI can see your current workspace
3. **Iterate**: Refine requests based on responses
4. **Code Review**: Always review generated code before using

### Keyboard Shortcuts:
- `Ctrl+Shift+P` → "Augment AI: Open Chat"
- In chat: `Enter` to send, `Shift+Enter` for new line

### Context Features:
- AI automatically sees your current file
- Workspace structure is analyzed
- Recent files are considered for context

## Security Notes

- API keys are stored securely in VS Code settings
- No data is logged locally by the extension
- All communication uses HTTPS
- Only your chosen AI provider receives your data

## Support

### Common Issues:
- **Rate Limits**: Wait and retry if you hit API limits
- **Large Files**: AI works best with focused code selections
- **Network Issues**: Check internet connection and firewall settings

### Getting Help:
- Check the README.md for detailed documentation
- Review error messages in VS Code Output panel
- File issues on the project repository

## Uninstallation

1. Open VS Code Extensions panel (`Ctrl+Shift+X`)
2. Find "Augment AI Assistant"
3. Click the gear icon → "Uninstall"
4. Restart VS Code

Your API keys and settings will remain in VS Code settings unless manually removed.

---

**Enjoy coding with your AI assistant! 🚀**
