# 🧪 CodeMind AI Testing Guide

## 🚀 **Complete Setup & Testing Instructions**

### **Prerequisites**
- VS Code 1.74.0+
- Node.js 16+ (for testing script)
- 8GB+ RAM recommended
- Internet connection (for initial model download)

---

## 📥 **Step 1: Install Ollama & Model**

### **Install Ollama:**
```bash
# macOS/Linux
curl -fsSL https://ollama.ai/install.sh | sh

# Windows: Download from https://ollama.ai/download/windows
```

### **Download DeepSeek Coder (4.1GB):**
```bash
ollama pull deepseek-coder:6.7b
```

### **Start Ollama:**
```bash
ollama serve
```

---

## 🔍 **Step 2: Test Ollama Connection**

### **Quick Test:**
```bash
ollama run deepseek-coder:6.7b "Write a hello world in Python"
```

### **Advanced Test (using our test script):**
```bash
cd /path/to/codemind-project
node test-ollama.js
```

**Expected Output:**
```
🚀 CodeMind AI - Ollama Integration Test

🔍 Testing Ollama connection...
✅ Ollama is running!
✅ DeepSeek Coder model is available!

🧪 Testing code generation...
✅ Code generation successful!
🤖 AI Response:
──────────────────────────────────────────────────
def factorial(n):
    if n == 0 or n == 1:
        return 1
    else:
        return n * factorial(n - 1)
──────────────────────────────────────────────────

🎉 All tests passed! CodeMind AI is ready to use with Ollama.
```

---

## 🔌 **Step 3: Install CodeMind AI Extension**

### **Method 1: Command Line**
```bash
code --install-extension codemind-ai-assistant-0.1.0.vsix
```

### **Method 2: VS Code UI**
1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P`)
3. Type "Extensions: Install from VSIX..."
4. Select `codemind-ai-assistant-0.1.0.vsix`
5. Restart VS Code

---

## ⚙️ **Step 4: Configure Extension**

### **Open Settings:**
- Press `Ctrl+,` (or `Cmd+,`)
- Search for "CodeMind AI"

### **Required Settings:**
```json
{
  "codemind-ai.apiProvider": "ollama",
  "codemind-ai.model": "deepseek-coder:6.7b",
  "codemind-ai.localEndpoint": "http://localhost:11434"
}
```

### **Optional Settings:**
```json
{
  "codemind-ai.maxTokens": 2000,
  "codemind-ai.temperature": 0.1,
  "codemind-ai.contextLines": 10
}
```

---

## 🧪 **Step 5: Test Extension Features**

### **Test 1: Chat Interface** ⭐
1. Press `Ctrl+Shift+P` → "CodeMind AI: Open Chat"
2. Type: "Hello! Can you help me write a Python function?"
3. **Expected**: AI responds with helpful message

### **Test 2: Code Explanation** ⭐⭐
1. Create a new file: `test.py`
2. Add this code:
   ```python
   def quicksort(arr):
       if len(arr) <= 1:
           return arr
       pivot = arr[len(arr) // 2]
       left = [x for x in arr if x < pivot]
       middle = [x for x in arr if x == pivot]
       right = [x for x in arr if x > pivot]
       return quicksort(left) + middle + quicksort(right)
   ```
3. Select all the code
4. Right-click → "Explain Selected Code"
5. **Expected**: AI explains quicksort algorithm

### **Test 3: Code Generation** ⭐⭐⭐
1. Press `Ctrl+Shift+P` → "CodeMind AI: Generate Code"
2. Type: "Create a REST API endpoint in Express.js for user registration"
3. **Expected**: AI generates Express.js code with proper structure

### **Test 4: Code Refactoring** ⭐⭐
1. Create this inefficient code:
   ```javascript
   function findMax(numbers) {
       var max = numbers[0];
       for (var i = 0; i < numbers.length; i++) {
           if (numbers[i] > max) {
               max = numbers[i];
           }
       }
       return max;
   }
   ```
2. Select the code
3. Right-click → "Refactor Code" → "Improve readability"
4. **Expected**: AI suggests modern ES6+ improvements

### **Test 5: Context Awareness** ⭐⭐⭐
1. Open a project with multiple files
2. In the chat, ask: "What kind of project is this?"
3. **Expected**: AI analyzes your workspace and describes the project

---

## 🔧 **Troubleshooting**

### **❌ "Connection refused" Error**
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# If not running:
ollama serve
```

### **❌ "Model not found" Error**
```bash
# List installed models
ollama list

# Install DeepSeek Coder if missing
ollama pull deepseek-coder:6.7b
```

### **❌ Extension Not Loading**
1. Restart VS Code
2. Check Output panel: View → Output → "CodeMind AI"
3. Ensure Ollama is running on port 11434

### **❌ Slow Responses**
1. Reduce max tokens: `"codemind-ai.maxTokens": 1000`
2. Set temperature to 0: `"codemind-ai.temperature": 0.0`
3. Close other applications to free RAM

### **❌ Poor Code Quality**
1. Try a larger model: `ollama pull deepseek-coder:33b`
2. Increase temperature: `"codemind-ai.temperature": 0.2`
3. Be more specific in your prompts

---

## 📊 **Performance Benchmarks**

### **Response Times (on M1 MacBook Pro):**
- **Simple questions**: 2-5 seconds
- **Code explanation**: 3-8 seconds  
- **Code generation**: 5-15 seconds
- **Complex refactoring**: 10-30 seconds

### **Memory Usage:**
- **Ollama + Model**: ~6GB RAM
- **VS Code Extension**: ~50MB RAM
- **Total**: ~6.1GB RAM

---

## 🎯 **Advanced Testing Scenarios**

### **Multi-Language Support:**
Test with Python, JavaScript, TypeScript, Rust, Go, Java, C++

### **Large Codebase:**
Open a real project and test context awareness

### **Error Handling:**
1. Stop Ollama service
2. Try using the extension
3. Verify proper error messages

### **Performance Under Load:**
1. Send multiple requests quickly
2. Verify responses remain consistent

---

## ✅ **Success Criteria**

Your CodeMind AI setup is working correctly if:

- ✅ Ollama responds to API calls
- ✅ Extension loads without errors
- ✅ Chat interface opens and responds
- ✅ Code explanation works on selected text
- ✅ Code generation produces working code
- ✅ Refactoring suggestions are relevant
- ✅ Response times are under 30 seconds
- ✅ No memory leaks or crashes

---

## 🚀 **Next Steps**

Once everything is working:

1. **Customize Settings**: Adjust temperature, tokens, etc.
2. **Try Different Models**: `codellama:13b`, `magicoder:7b`
3. **Integrate into Workflow**: Use for daily coding tasks
4. **Share Feedback**: Report issues or suggestions

**🎉 Congratulations! You now have a fully functional, FREE, local AI coding assistant!**
