{"name": "augment-ai-assistant", "displayName": "Augment AI Assistant", "description": "An AI-powered coding assistant similar to Augment Agent, integrated directly into VS Code", "version": "0.1.0", "publisher": "augment-ai", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets"], "keywords": ["ai", "assistant", "coding", "augment", "chatbot", "code-generation"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augment-ai.openChat", "title": "Open Augment AI Chat", "category": "Augment AI"}, {"command": "augment-ai.explainCode", "title": "Explain Selected Code", "category": "Augment AI"}, {"command": "augment-ai.generateCode", "title": "Generate Code", "category": "Augment AI"}, {"command": "augment-ai.refactorCode", "title": "Refactor Code", "category": "Augment AI"}], "menus": {"editor/context": [{"command": "augment-ai.explainCode", "when": "editorHasSelection", "group": "augment-ai"}, {"command": "augment-ai.refactorCode", "when": "editorHasSelection", "group": "augment-ai"}], "commandPalette": [{"command": "augment-ai.openChat"}, {"command": "augment-ai.explainCode"}, {"command": "augment-ai.generateCode"}, {"command": "augment-ai.refactorCode"}]}, "views": {"explorer": [{"id": "augment-ai.chatView", "name": "Augment AI Chat", "when": "true"}]}, "configuration": {"title": "Augment AI Assistant", "properties": {"augment-ai.apiProvider": {"type": "string", "enum": ["openai", "anthropic"], "default": "anthropic", "description": "AI API provider to use"}, "augment-ai.apiKey": {"type": "string", "default": "", "description": "API key for the selected AI provider"}, "augment-ai.model": {"type": "string", "default": "claude-3-sonnet-20240229", "description": "AI model to use"}, "augment-ai.maxTokens": {"type": "number", "default": 4000, "description": "Maximum tokens for AI responses"}, "augment-ai.temperature": {"type": "number", "default": 0.1, "minimum": 0, "maximum": 2, "description": "Temperature for AI responses (0 = deterministic, 2 = very creative)"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "16.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0", "eslint": "^8.28.0", "glob": "^11.0.3", "mocha": "^11.7.0", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0", "marked": "^9.1.0"}, "author": "Augment AI", "license": "MIT"}