{"name": "codemind-ai-assistant", "displayName": "CodeMind AI Assistant", "description": "An intelligent AI-powered coding companion that understands your code and helps you build better software", "version": "0.1.0", "publisher": "codemind-ai", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets"], "keywords": ["ai", "assistant", "coding", "codemind", "chatbot", "code-generation", "intelligent", "companion"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "codemind-ai.openChat", "title": "Open CodeMind AI Chat", "category": "CodeMind AI"}, {"command": "codemind-ai.explainCode", "title": "Explain Selected Code", "category": "CodeMind AI"}, {"command": "codemind-ai.generateCode", "title": "Generate Code", "category": "CodeMind AI"}, {"command": "codemind-ai.refactorCode", "title": "Refactor Code", "category": "CodeMind AI"}], "menus": {"editor/context": [{"command": "codemind-ai.explainCode", "when": "editorHasSelection", "group": "codemind-ai"}, {"command": "codemind-ai.refactorCode", "when": "editorHasSelection", "group": "codemind-ai"}], "commandPalette": [{"command": "codemind-ai.openChat"}, {"command": "codemind-ai.explainCode"}, {"command": "codemind-ai.generateCode"}, {"command": "codemind-ai.refactorCode"}]}, "views": {"explorer": [{"id": "codemind-ai.chatView", "name": "CodeMind AI Chat", "when": "true"}]}, "configuration": {"title": "CodeMind AI Assistant", "properties": {"codemind-ai.apiProvider": {"type": "string", "enum": ["openai", "anthropic", "ollama", "groq", "huggingface"], "default": "ollama", "description": "AI API provider to use"}, "codemind-ai.apiKey": {"type": "string", "default": "", "description": "API key for the selected AI provider"}, "codemind-ai.model": {"type": "string", "default": "deepseek-coder:6.7b", "description": "AI model to use (e.g., deepseek-coder:6.7b for Ollama, gpt-4 for OpenAI)"}, "codemind-ai.localEndpoint": {"type": "string", "default": "http://localhost:11434", "description": "Local endpoint for Ollama or other local models"}, "codemind-ai.maxTokens": {"type": "number", "default": 4000, "description": "Maximum tokens for AI responses"}, "codemind-ai.temperature": {"type": "number", "default": 0.1, "minimum": 0, "maximum": 2, "description": "Temperature for AI responses (0 = deterministic, 2 = very creative)"}, "codemind-ai.autoSaveConversation": {"type": "boolean", "default": true, "description": "Automatically save conversation history"}, "codemind-ai.contextLines": {"type": "number", "default": 10, "minimum": 5, "maximum": 50, "description": "Number of context lines to include around selected code"}, "codemind-ai.enableCodeSuggestions": {"type": "boolean", "default": true, "description": "Enable automatic code suggestions while typing"}, "codemind-ai.responseFormat": {"type": "string", "enum": ["markdown", "plain"], "default": "markdown", "description": "Format for AI responses"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "16.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0", "eslint": "^8.28.0", "glob": "^11.0.3", "mocha": "^11.7.0", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0", "marked": "^9.1.0"}, "author": "CodeMind AI", "license": "MIT"}