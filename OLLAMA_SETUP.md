# 🚀 CodeMind AI with Ollama Setup Guide

## 📊 **Recommended Model: DeepSeek Coder 6.7B**

**Model Details:**
- **Name**: `deepseek-coder:6.7b`
- **Size**: ~4.1GB download
- **Performance**: Excellent code generation (beats Code<PERSON>lama in benchmarks)
- **Speed**: Fast inference on consumer hardware
- **Languages**: 87+ programming languages
- **Context**: 16K tokens
- **Requirements**: 8GB+ RAM recommended

## 🔧 **Step 1: Install Ollama**

### macOS:
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

### Windows:
Download from: https://ollama.ai/download/windows

### Linux:
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

## 📥 **Step 2: Download DeepSeek Coder Model**

```bash
# Download the model (4.1GB)
ollama pull deepseek-coder:6.7b

# Verify installation
ollama list
```

**Alternative Models (if you want to try others):**
```bash
# Smaller, faster model (2.7GB)
ollama pull deepseek-coder:1.3b

# Larger, more capable model (8.4GB) 
ollama pull deepseek-coder:33b

# Code-focused Llama model (7.3GB)
ollama pull codellama:13b

# Magicoder - excellent for code completion (4.1GB)
ollama pull magicoder:7b
```

## 🎯 **Step 3: Test Ollama**

```bash
# Start Ollama service (if not auto-started)
ollama serve

# Test the model
ollama run deepseek-coder:6.7b "Write a Python function to calculate fibonacci numbers"
```

## 🔌 **Step 4: Install CodeMind AI Extension**

1. **Install the extension:**
   ```bash
   code --install-extension codemind-ai-assistant-0.1.0.vsix
   ```

2. **Or manually in VS Code:**
   - Open VS Code
   - Press `Ctrl+Shift+P` → "Extensions: Install from VSIX..."
   - Select `codemind-ai-assistant-0.1.0.vsix`

## ⚙️ **Step 5: Configure CodeMind AI**

1. **Open VS Code Settings** (`Ctrl+,` or `Cmd+,`)
2. **Search for "CodeMind AI"**
3. **Configure these settings:**

```json
{
  "codemind-ai.apiProvider": "ollama",
  "codemind-ai.model": "deepseek-coder:6.7b",
  "codemind-ai.localEndpoint": "http://localhost:11434",
  "codemind-ai.maxTokens": 2000,
  "codemind-ai.temperature": 0.1
}
```

**No API key needed for Ollama!** 🎉

## 🧪 **Step 6: Test CodeMind AI**

### **Test 1: Open Chat Interface**
1. Press `Ctrl+Shift+P` → "CodeMind AI: Open Chat"
2. Type: "Hello! Can you help me write code?"
3. You should get a response from the local AI

### **Test 2: Code Explanation**
1. Create a new file with some code:
   ```python
   def bubble_sort(arr):
       n = len(arr)
       for i in range(n):
           for j in range(0, n-i-1):
               if arr[j] > arr[j+1]:
                   arr[j], arr[j+1] = arr[j+1], arr[j]
       return arr
   ```
2. Select the code
3. Right-click → "Explain Selected Code"
4. AI should explain the bubble sort algorithm

### **Test 3: Code Generation**
1. Press `Ctrl+Shift+P` → "CodeMind AI: Generate Code"
2. Type: "Create a REST API endpoint in Express.js for user authentication"
3. AI should generate working Express.js code

### **Test 4: Code Refactoring**
1. Select some code that could be improved
2. Right-click → "Refactor Code"
3. Choose "Improve readability"
4. AI should suggest improvements

## 🚀 **Performance Optimization**

### **For Better Speed:**
```json
{
  "codemind-ai.maxTokens": 1000,
  "codemind-ai.temperature": 0.0
}
```

### **For Better Quality:**
```json
{
  "codemind-ai.maxTokens": 4000,
  "codemind-ai.temperature": 0.2
}
```

### **Hardware Recommendations:**
- **Minimum**: 8GB RAM, any modern CPU
- **Recommended**: 16GB RAM, 8+ core CPU
- **Optimal**: 32GB RAM, GPU acceleration (coming soon)

## 🔧 **Troubleshooting**

### **"Connection refused" error:**
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# If not running, start it:
ollama serve
```

### **Model not found:**
```bash
# List installed models
ollama list

# Pull the model if missing
ollama pull deepseek-coder:6.7b
```

### **Slow responses:**
- Reduce `maxTokens` to 1000-2000
- Set `temperature` to 0.0
- Close other applications to free RAM

### **Extension not loading:**
- Restart VS Code
- Check Output panel for errors
- Ensure Ollama is running

## 📊 **Model Comparison**

| Model | Size | Speed | Quality | Best For |
|-------|------|-------|---------|----------|
| deepseek-coder:1.3b | 2.7GB | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | Quick completions |
| deepseek-coder:6.7b | 4.1GB | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | **Recommended** |
| codellama:13b | 7.3GB | ⭐⭐⭐ | ⭐⭐⭐⭐ | Complex logic |
| deepseek-coder:33b | 8.4GB | ⭐⭐ | ⭐⭐⭐⭐⭐ | Best quality |

## 🎯 **Usage Tips**

1. **Be Specific**: "Create a React component with TypeScript for a user profile card"
2. **Provide Context**: Select relevant code before asking questions
3. **Iterate**: Refine your requests based on responses
4. **Use Chat**: The chat interface maintains context across messages

## 🔄 **Switching Models**

To try a different model:
1. Download it: `ollama pull codellama:13b`
2. Update VS Code settings: `"codemind-ai.model": "codellama:13b"`
3. Restart the chat interface

---

**🎉 You're all set! CodeMind AI is now running locally with no API costs!**
