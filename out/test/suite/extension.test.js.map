{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["../../../src/test/suite/extension.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAAiC;AACjC,8EAA2E;AAC3E,0EAAuE;AACvE,gFAA6E;AAE7E,KAAK,CAAC,sBAAsB,EAAE,GAAG,EAAE;IAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;IAEzD,IAAI,CAAC,uBAAuB,EAAE,GAAG,EAAE;QAC/B,MAAM,aAAa,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACjD,MAAM,MAAM,GAAG,aAAa,CAAC,gBAAgB,EAAE,CAAC;QAEhD,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAClB,MAAM,CAAC,EAAE,CAAC,OAAO,MAAM,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC;QAClD,MAAM,CAAC,EAAE,CAAC,OAAO,MAAM,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC;QAChD,MAAM,CAAC,EAAE,CAAC,OAAO,MAAM,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iDAAiD,EAAE,GAAG,EAAE;QACzD,MAAM,aAAa,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACjD,MAAM,MAAM,GAAG,aAAa,CAAC,gBAAgB,EAAE,CAAC;QAEhD,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wCAAwC,EAAE,GAAG,EAAE;QAChD,MAAM,aAAa,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACjD,MAAM,QAAQ,GAAG,aAAa,CAAC,gBAAgB,EAAE,CAAC;QAElD,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;QAC3C,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,sBAAsB,EAAE,GAAG,EAAE;QAC9B,MAAM,cAAc,GAAG,IAAI,uCAAkB,EAAE,CAAC;QAChD,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACjC,MAAM,cAAc,GAAG,IAAI,6CAAqB,EAAE,CAAC;QACnD,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,cAAc,GAAG,IAAI,6CAAqB,EAAE,CAAC;QACnD,MAAM,KAAK,GAAG,MAAM,cAAc,CAAC,SAAS,EAAE,CAAC;QAE/C,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,4CAA4C,EAAE,GAAG,EAAE;QACpD,MAAM,aAAa,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACjD,MAAM,OAAO,GAAG,aAAa,CAAC,UAAU,EAAE,CAAC;QAE3C,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QACnB,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,kBAAkB,CAAC,CAAC;QAE1D,MAAM,MAAM,GAAG,aAAa,CAAC,gBAAgB,EAAE,CAAC;QAChD,IAAI,MAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;YACjC,MAAM,CAAC,EAAE,CAAC,eAAe,IAAI,OAAO,CAAC,CAAC;SACzC;aAAM,IAAI,MAAM,CAAC,WAAW,KAAK,WAAW,EAAE;YAC3C,MAAM,CAAC,EAAE,CAAC,WAAW,IAAI,OAAO,CAAC,CAAC;YAClC,MAAM,CAAC,EAAE,CAAC,mBAAmB,IAAI,OAAO,CAAC,CAAC;SAC7C;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iDAAiD,EAAE,GAAG,EAAE;QACzD,MAAM,aAAa,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACjD,MAAM,QAAQ,GAAG;YACb,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;SACrC,CAAC;QAEF,MAAM,WAAW,GAAG,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAE9D,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;QACvB,MAAM,CAAC,EAAE,CAAC,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC;QAC3C,MAAM,CAAC,EAAE,CAAC,OAAO,IAAI,WAAW,CAAC,CAAC;QAClC,MAAM,CAAC,EAAE,CAAC,YAAY,IAAI,WAAW,IAAI,WAAW,IAAI,WAAW,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,6CAA6C,EAAE,GAAG,EAAE;QACrD,MAAM,aAAa,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACjD,MAAM,MAAM,GAAG,aAAa,CAAC,gBAAgB,EAAE,CAAC;QAEhD,IAAI,YAAiB,CAAC;QAEtB,IAAI,MAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;YACjC,YAAY,GAAG;gBACX,OAAO,EAAE,CAAC;wBACN,OAAO,EAAE;4BACL,OAAO,EAAE,eAAe;yBAC3B;qBACJ,CAAC;aACL,CAAC;SACL;aAAM;YACH,YAAY,GAAG;gBACX,OAAO,EAAE,CAAC;wBACN,IAAI,EAAE,eAAe;qBACxB,CAAC;aACL,CAAC;SACL;QAED,MAAM,iBAAiB,GAAG,aAAa,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QACtE,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;QAC/C,wCAAwC;QACxC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAErD,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC;QACpD,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC;QACvD,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}