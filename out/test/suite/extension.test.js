"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const ConfigurationManager_1 = require("../../managers/ConfigurationManager");
const CodeContextManager_1 = require("../../managers/CodeContextManager");
const FileOperationsManager_1 = require("../../managers/FileOperationsManager");
suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');
    test('Configuration Manager', () => {
        const configManager = new ConfigurationManager_1.ConfigurationManager();
        const config = configManager.getConfiguration();
        assert.ok(config);
        assert.ok(typeof config.apiProvider === 'string');
        assert.ok(typeof config.maxTokens === 'number');
        assert.ok(typeof config.temperature === 'number');
    });
    test('Configuration Manager - API Provider Validation', () => {
        const configManager = new ConfigurationManager_1.ConfigurationManager();
        const config = configManager.getConfiguration();
        assert.ok(['openai', 'anthropic'].includes(config.apiProvider));
    });
    test('Configuration Manager - Model Endpoint', () => {
        const configManager = new ConfigurationManager_1.ConfigurationManager();
        const endpoint = configManager.getModelEndpoint();
        assert.ok(endpoint.startsWith('https://'));
        assert.ok(endpoint.includes('api'));
    });
    test('Code Context Manager', () => {
        const contextManager = new CodeContextManager_1.CodeContextManager();
        assert.ok(contextManager);
    });
    test('File Operations Manager', () => {
        const fileOpsManager = new FileOperationsManager_1.FileOperationsManager();
        assert.ok(fileOpsManager);
    });
    test('File Operations Manager - List Files', async () => {
        const fileOpsManager = new FileOperationsManager_1.FileOperationsManager();
        const files = await fileOpsManager.listFiles();
        assert.ok(Array.isArray(files));
    });
    test('Configuration Manager - Headers Generation', () => {
        const configManager = new ConfigurationManager_1.ConfigurationManager();
        const headers = configManager.getHeaders();
        assert.ok(headers);
        assert.ok(headers['Content-Type'] === 'application/json');
        const config = configManager.getConfiguration();
        if (config.apiProvider === 'openai') {
            assert.ok('Authorization' in headers);
        }
        else if (config.apiProvider === 'anthropic') {
            assert.ok('x-api-key' in headers);
            assert.ok('anthropic-version' in headers);
        }
    });
    test('Configuration Manager - Request Body Formatting', () => {
        const configManager = new ConfigurationManager_1.ConfigurationManager();
        const messages = [
            { role: 'user', content: 'Hello' }
        ];
        const requestBody = configManager.formatRequestBody(messages);
        assert.ok(requestBody);
        assert.ok(typeof requestBody === 'object');
        assert.ok('model' in requestBody);
        assert.ok('max_tokens' in requestBody || 'maxTokens' in requestBody);
    });
    test('Configuration Manager - Response Extraction', () => {
        const configManager = new ConfigurationManager_1.ConfigurationManager();
        const config = configManager.getConfiguration();
        let mockResponse;
        if (config.apiProvider === 'openai') {
            mockResponse = {
                choices: [{
                        message: {
                            content: 'Test response'
                        }
                    }]
            };
        }
        else {
            mockResponse = {
                content: [{
                        text: 'Test response'
                    }]
            };
        }
        const extractedResponse = configManager.extractResponse(mockResponse);
        assert.strictEqual(extractedResponse, 'Test response');
    });
    test('Extension Commands Registration', async () => {
        // Test that our commands are registered
        const commands = await vscode.commands.getCommands();
        assert.ok(commands.includes('augment-ai.openChat'));
        assert.ok(commands.includes('augment-ai.explainCode'));
        assert.ok(commands.includes('augment-ai.generateCode'));
        assert.ok(commands.includes('augment-ai.refactorCode'));
    });
});
//# sourceMappingURL=extension.test.js.map