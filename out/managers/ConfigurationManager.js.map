{"version": 3, "file": "ConfigurationManager.js", "sourceRoot": "", "sources": ["../../src/managers/ConfigurationManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAUjC,MAAa,oBAAoB;IAG7B;QACI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3C,CAAC;IAEO,iBAAiB;QACrB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAEhE,OAAO;YACH,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC;YACnD,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC;YAChC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,0BAA0B,CAAC;YACtD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;YACxC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC;SAC9C,CAAC;IACN,CAAC;IAEM,gBAAgB;QACnB,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC9B,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3C,CAAC;IAEM,YAAY;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACzC,CAAC;IAEM,KAAK,CAAC,eAAe;QACxB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC5C,MAAM,EAAE,cAAc,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,UAAU;YACrE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,QAAQ;SACxB,CAAC,CAAC;QAEH,IAAI,MAAM,EAAE;YACR,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,MAAM,CACzD,QAAQ,EACR,MAAM,EACN,MAAM,CAAC,mBAAmB,CAAC,MAAM,CACpC,CAAC;YACF,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,gBAAgB;QACnB,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC7B,KAAK,QAAQ;gBACT,OAAO,4CAA4C,CAAC;YACxD,KAAK,WAAW;gBACZ,OAAO,uCAAuC,CAAC;YACnD;gBACI,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;SAC/E;IACL,CAAC;IAEM,UAAU;QACb,MAAM,OAAO,GAA2B;YACpC,cAAc,EAAE,kBAAkB;SACrC,CAAC;QAEF,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC7B,KAAK,QAAQ;gBACT,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBAC1D,MAAM;YACV,KAAK,WAAW;gBACZ,OAAO,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;gBAC1C,OAAO,CAAC,mBAAmB,CAAC,GAAG,YAAY,CAAC;gBAC5C,MAAM;SACb;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,iBAAiB,CAAC,QAAgD;QACrE,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC7B,KAAK,QAAQ;gBACT,OAAO;oBACH,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;oBACxB,QAAQ,EAAE,QAAQ;oBAClB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;oBACjC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;iBACvC,CAAC;YACN,KAAK,WAAW;gBACZ,uCAAuC;gBACvC,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;gBAC9D,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;gBAE/D,OAAO;oBACH,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;oBACxB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;oBACjC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;oBACpC,MAAM,EAAE,aAAa,EAAE,OAAO,IAAI,EAAE;oBACpC,QAAQ,EAAE,YAAY;iBACzB,CAAC;YACN;gBACI,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;SAC/E;IACL,CAAC;IAEM,eAAe,CAAC,QAAa;QAChC,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC7B,KAAK,QAAQ;gBACT,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,sBAAsB,CAAC;YAC7E,KAAK,WAAW;gBACZ,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,sBAAsB,CAAC;YACjE;gBACI,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;SAC/E;IACL,CAAC;CACJ;AApHD,oDAoHC"}