{"version": 3, "file": "ConfigurationManager.js", "sourceRoot": "", "sources": ["../../src/managers/ConfigurationManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAWjC,MAAa,oBAAoB;IAG7B;QACI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3C,CAAC;IAEO,iBAAiB;QACrB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAEhE,OAAO;YACH,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC;YACnD,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC;YAChC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,0BAA0B,CAAC;YACtD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;YACxC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC;YAC3C,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,wBAAwB,CAAC;SACvE,CAAC;IACN,CAAC;IAEM,gBAAgB;QACnB,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC9B,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3C,CAAC;IAEM,YAAY;QACf,oCAAoC;QACpC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;YACtC,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACzC,CAAC;IAEM,KAAK,CAAC,eAAe;QACxB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC5C,MAAM,EAAE,cAAc,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,UAAU;YACrE,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,QAAQ;SACxB,CAAC,CAAC;QAEH,IAAI,MAAM,EAAE;YACR,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,MAAM,CACzD,QAAQ,EACR,MAAM,EACN,MAAM,CAAC,mBAAmB,CAAC,MAAM,CACpC,CAAC;YACF,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,gBAAgB;QACnB,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC7B,KAAK,QAAQ;gBACT,OAAO,4CAA4C,CAAC;YACxD,KAAK,WAAW;gBACZ,OAAO,uCAAuC,CAAC;YACnD,KAAK,QAAQ;gBACT,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,eAAe,CAAC;YACvD,KAAK,MAAM;gBACP,OAAO,iDAAiD,CAAC;YAC7D,KAAK,aAAa;gBACd,OAAO,+CAA+C,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC9E;gBACI,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;SAC/E;IACL,CAAC;IAEM,UAAU;QACb,MAAM,OAAO,GAA2B;YACpC,cAAc,EAAE,kBAAkB;SACrC,CAAC;QAEF,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC7B,KAAK,QAAQ;gBACT,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBAC1D,MAAM;YACV,KAAK,WAAW;gBACZ,OAAO,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;gBAC1C,OAAO,CAAC,mBAAmB,CAAC,GAAG,YAAY,CAAC;gBAC5C,MAAM;YACV,KAAK,MAAM;gBACP,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBAC1D,MAAM;YACV,KAAK,aAAa;gBACd,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBACpB,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;iBAC7D;gBACD,MAAM;YACV,KAAK,QAAQ;gBACT,mDAAmD;gBACnD,MAAM;SACb;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,iBAAiB,CAAC,QAAgD;QACrE,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC7B,KAAK,QAAQ,CAAC;YACd,KAAK,MAAM;gBACP,OAAO;oBACH,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;oBACxB,QAAQ,EAAE,QAAQ;oBAClB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;oBACjC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;iBACvC,CAAC;YACN,KAAK,WAAW;gBACZ,uCAAuC;gBACvC,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;gBAC9D,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;gBAE/D,OAAO;oBACH,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;oBACxB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;oBACjC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;oBACpC,MAAM,EAAE,aAAa,EAAE,OAAO,IAAI,EAAE;oBACpC,QAAQ,EAAE,YAAY;iBACzB,CAAC;YACN,KAAK,QAAQ;gBACT,4DAA4D;gBAC5D,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBAC5B,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ;wBAAE,OAAO,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC;oBACvD,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM;wBAAE,OAAO,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;oBACnD,IAAI,CAAC,CAAC,IAAI,KAAK,WAAW;wBAAE,OAAO,cAAc,CAAC,CAAC,OAAO,EAAE,CAAC;oBAC7D,OAAO,CAAC,CAAC,OAAO,CAAC;gBACrB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAEhB,OAAO;oBACH,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;oBACxB,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,KAAK;oBACb,OAAO,EAAE;wBACL,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;wBACpC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;qBACrC;iBACJ,CAAC;YACN,KAAK,aAAa;gBACd,sBAAsB;gBACtB,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzD,OAAO;oBACH,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE;wBACR,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;wBACrC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;wBACpC,gBAAgB,EAAE,KAAK;qBAC1B;iBACJ,CAAC;YACN;gBACI,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;SAC/E;IACL,CAAC;IAEM,eAAe,CAAC,QAAa;QAChC,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC7B,KAAK,QAAQ,CAAC;YACd,KAAK,MAAM;gBACP,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,sBAAsB,CAAC;YAC7E,KAAK,WAAW;gBACZ,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,sBAAsB,CAAC;YACjE,KAAK,QAAQ;gBACT,OAAO,QAAQ,CAAC,QAAQ,IAAI,sBAAsB,CAAC;YACvD,KAAK,aAAa;gBACd,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBACzB,OAAO,QAAQ,CAAC,CAAC,CAAC,EAAE,cAAc,IAAI,sBAAsB,CAAC;iBAChE;gBACD,OAAO,QAAQ,CAAC,cAAc,IAAI,sBAAsB,CAAC;YAC7D;gBACI,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;SAC/E;IACL,CAAC;CACJ;AAhLD,oDAgLC"}