{"version": 3, "file": "FileOperationsManager.js", "sourceRoot": "", "sources": ["../../src/managers/FileOperationsManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAc7B,MAAa,qBAAqB;IAEvB,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,OAAe;QACrD,IAAI;YACA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,IAAI,CAAC,eAAe,EAAE;gBAClB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;gBAC5D,OAAO,KAAK,CAAC;aAChB;YAED,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEpE,+BAA+B;YAC/B,IAAI;gBACA,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzC,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACpD,QAAQ,QAAQ,6BAA6B,EAC7C,KAAK,EAAE,IAAI,CACd,CAAC;gBACF,IAAI,SAAS,KAAK,KAAK,EAAE;oBACrB,OAAO,KAAK,CAAC;iBAChB;aACJ;YAAC,MAAM;gBACJ,4CAA4C;aAC/C;YAED,uCAAuC;YACvC,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjF,IAAI;gBACA,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;aACtD;YAAC,MAAM;gBACJ,gCAAgC;aACnC;YAED,aAAa;YACb,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;YAEvE,gBAAgB;YAChB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAE/C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,QAAQ,uBAAuB,CAAC,CAAC;YAC9E,OAAO,IAAI,CAAC;SACf;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,KAAiB;QACvD,IAAI;YACA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,IAAI,CAAC,eAAe,EAAE;gBAClB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;gBAC5D,OAAO,KAAK,CAAC;aAChB;YAED,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YACpE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAEnE,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YAEjD,wEAAwE;YACxE,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAEjF,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;gBAC5B,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aACjE;YAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAEhE,IAAI,OAAO,EAAE;gBACT,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACtB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,QAAQ,wBAAwB,CAAC,CAAC;gBAC/E,OAAO,IAAI,CAAC;aACf;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;gBACvE,OAAO,KAAK,CAAC;aAChB;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAAe;QACvC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;SAChB;QAED,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC5C,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBACzC,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,IAAI,OAAO,EAAE;gBACT,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;gBACtE,OAAO,IAAI,CAAC;aACf;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;gBAC3D,OAAO,KAAK,CAAC;aAChB;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YACrE,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,OAAe;QACzC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;SAChB;QAED,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC5C,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;gBACnC,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YAEH,IAAI,OAAO,EAAE;gBACT,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,iCAAiC,CAAC,CAAC;gBACxE,OAAO,IAAI,CAAC;aACf;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;gBAC9D,OAAO,KAAK,CAAC;aAChB;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;YACxE,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAClC,IAAI;YACA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,IAAI,CAAC,eAAe,EAAE;gBAClB,OAAO,IAAI,CAAC;aACf;YAED,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YACpE,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACjE,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;YAClC,OAAO,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;SACtC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,uBAAuB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,YAAoB,EAAE,EAAE,OAAgB;QAC3D,IAAI;YACA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,IAAI,CAAC,eAAe,EAAE;gBAClB,OAAO,EAAE,CAAC;aACb;YAED,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAEjE,IAAI,KAAK,GAAG,OAAO;iBACd,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;iBACvD,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;YAEjD,IAAI,OAAO,EAAE;gBACT,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;gBAClC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;aAClD;YAED,OAAO,KAAK,CAAC;SAChB;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,2BAA2B,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,WAAoB;QAC/D,MAAM,OAAO,GAAyD,EAAE,CAAC;QAEzE,IAAI;YACA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,IAAI,CAAC,eAAe,EAAE;gBAClB,OAAO,OAAO,CAAC;aAClB;YAED,mDAAmD;YACnD,MAAM,OAAO,GAAG,WAAW,IAAI,MAAM,CAAC;YACtC,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,oBAAoB,EAAE,GAAG,CAAC,CAAC;YAEnF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACtB,IAAI;oBACA,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBAC/D,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;oBAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAE/B,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;wBAC1B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,EAAE;4BACvD,OAAO,CAAC,IAAI,CAAC;gCACT,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC;gCAC3C,IAAI,EAAE,KAAK,GAAG,CAAC;gCACf,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE;6BACvB,CAAC,CAAC;yBACN;oBACL,CAAC,CAAC,CAAC;iBACN;gBAAC,OAAO,KAAK,EAAE;oBACZ,gCAAgC;oBAChC,SAAS;iBACZ;aACJ;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;SACtD;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,QAAgB;QACrC,IAAI;YACA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,IAAI,CAAC,eAAe,EAAE;gBAClB,OAAO,IAAI,CAAC;aACf;YAED,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YACpE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEtD,OAAO;gBACH,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;aACjC,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,+BAA+B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,QAAgB;QACpC,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC,OAAO,EAAE;gBACV,OAAO,IAAI,CAAC;aACf;YAED,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACjE,MAAM,UAAU,GAAG,GAAG,QAAQ,WAAW,SAAS,EAAE,CAAC;YAErD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAC3D,OAAO,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;SACtC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,yBAAyB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAEO,kBAAkB;QACtB,OAAO,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAAC,UAA2B;QAC1D,IAAI,aAAa,GAAG,IAAI,CAAC;QAEzB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAChC,IAAI;gBACA,QAAQ,SAAS,CAAC,IAAI,EAAE;oBACpB,KAAK,QAAQ;wBACT,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,EAAE;4BACjC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;4BAC7E,aAAa,GAAG,aAAa,IAAI,OAAO,CAAC;yBAC5C;wBACD,MAAM;oBACV,KAAK,QAAQ;wBACT,IAAI,SAAS,CAAC,OAAO,EAAE;4BACnB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;4BAC7E,aAAa,GAAG,aAAa,IAAI,OAAO,CAAC;yBAC5C;wBACD,MAAM;oBACV,KAAK,QAAQ;wBACT,uCAAuC;wBACvC,MAAM;iBACb;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,+BAA+B,SAAS,CAAC,IAAI,OAAO,SAAS,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBAChG,aAAa,GAAG,KAAK,CAAC;aACzB;SACJ;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;CACJ;AArSD,sDAqSC"}