"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileOperationsManager = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
class FileOperationsManager {
    async createFile(filePath, content) {
        try {
            const workspaceFolder = this.getWorkspaceFolder();
            if (!workspaceFolder) {
                vscode.window.showErrorMessage('No workspace folder found');
                return false;
            }
            const fullPath = vscode.Uri.joinPath(workspaceFolder.uri, filePath);
            // Check if file already exists
            try {
                await vscode.workspace.fs.stat(fullPath);
                const overwrite = await vscode.window.showWarningMessage(`File ${filePath} already exists. Overwrite?`, 'Yes', 'No');
                if (overwrite !== 'Yes') {
                    return false;
                }
            }
            catch {
                // File doesn't exist, which is what we want
            }
            // Create directory if it doesn't exist
            const dirPath = vscode.Uri.joinPath(workspaceFolder.uri, path.dirname(filePath));
            try {
                await vscode.workspace.fs.createDirectory(dirPath);
            }
            catch {
                // Directory might already exist
            }
            // Write file
            const encoder = new TextEncoder();
            await vscode.workspace.fs.writeFile(fullPath, encoder.encode(content));
            // Open the file
            const document = await vscode.workspace.openTextDocument(fullPath);
            await vscode.window.showTextDocument(document);
            vscode.window.showInformationMessage(`File ${filePath} created successfully`);
            return true;
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to create file: ${error}`);
            return false;
        }
    }
    async modifyFile(filePath, edits) {
        try {
            const workspaceFolder = this.getWorkspaceFolder();
            if (!workspaceFolder) {
                vscode.window.showErrorMessage('No workspace folder found');
                return false;
            }
            const fullPath = vscode.Uri.joinPath(workspaceFolder.uri, filePath);
            const document = await vscode.workspace.openTextDocument(fullPath);
            const workspaceEdit = new vscode.WorkspaceEdit();
            // Sort edits by position (from end to beginning to avoid offset issues)
            const sortedEdits = edits.sort((a, b) => b.range.start.compareTo(a.range.start));
            for (const edit of sortedEdits) {
                workspaceEdit.replace(document.uri, edit.range, edit.newText);
            }
            const success = await vscode.workspace.applyEdit(workspaceEdit);
            if (success) {
                await document.save();
                vscode.window.showInformationMessage(`File ${filePath} modified successfully`);
                return true;
            }
            else {
                vscode.window.showErrorMessage(`Failed to apply edits to ${filePath}`);
                return false;
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to modify file: ${error}`);
            return false;
        }
    }
    async insertAtCursor(content) {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return false;
        }
        try {
            const success = await editor.edit(editBuilder => {
                const position = editor.selection.active;
                editBuilder.insert(position, content);
            });
            if (success) {
                vscode.window.showInformationMessage('Content inserted successfully');
                return true;
            }
            else {
                vscode.window.showErrorMessage('Failed to insert content');
                return false;
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to insert content: ${error}`);
            return false;
        }
    }
    async replaceSelection(content) {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return false;
        }
        try {
            const success = await editor.edit(editBuilder => {
                const selection = editor.selection;
                editBuilder.replace(selection, content);
            });
            if (success) {
                vscode.window.showInformationMessage('Selection replaced successfully');
                return true;
            }
            else {
                vscode.window.showErrorMessage('Failed to replace selection');
                return false;
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to replace selection: ${error}`);
            return false;
        }
    }
    async readFile(filePath) {
        try {
            const workspaceFolder = this.getWorkspaceFolder();
            if (!workspaceFolder) {
                return null;
            }
            const fullPath = vscode.Uri.joinPath(workspaceFolder.uri, filePath);
            const fileContent = await vscode.workspace.fs.readFile(fullPath);
            const decoder = new TextDecoder();
            return decoder.decode(fileContent);
        }
        catch (error) {
            console.error(`Failed to read file ${filePath}:`, error);
            return null;
        }
    }
    async listFiles(directory = '', pattern) {
        try {
            const workspaceFolder = this.getWorkspaceFolder();
            if (!workspaceFolder) {
                return [];
            }
            const dirPath = vscode.Uri.joinPath(workspaceFolder.uri, directory);
            const entries = await vscode.workspace.fs.readDirectory(dirPath);
            let files = entries
                .filter(([name, type]) => type === vscode.FileType.File)
                .map(([name]) => path.join(directory, name));
            if (pattern) {
                const regex = new RegExp(pattern);
                files = files.filter(file => regex.test(file));
            }
            return files;
        }
        catch (error) {
            console.error(`Failed to list files in ${directory}:`, error);
            return [];
        }
    }
    async searchInFiles(searchTerm, filePattern) {
        const results = [];
        try {
            const workspaceFolder = this.getWorkspaceFolder();
            if (!workspaceFolder) {
                return results;
            }
            // Use VS Code's findFiles and then search manually
            const pattern = filePattern || '**/*';
            const files = await vscode.workspace.findFiles(pattern, '**/node_modules/**', 100);
            for (const file of files) {
                try {
                    const document = await vscode.workspace.openTextDocument(file);
                    const text = document.getText();
                    const lines = text.split('\n');
                    lines.forEach((line, index) => {
                        if (line.toLowerCase().includes(searchTerm.toLowerCase())) {
                            results.push({
                                file: vscode.workspace.asRelativePath(file),
                                line: index + 1,
                                content: line.trim()
                            });
                        }
                    });
                }
                catch (error) {
                    // Skip files that can't be read
                    continue;
                }
            }
        }
        catch (error) {
            console.error('Failed to search in files:', error);
        }
        return results;
    }
    async getFileInfo(filePath) {
        try {
            const workspaceFolder = this.getWorkspaceFolder();
            if (!workspaceFolder) {
                return null;
            }
            const fullPath = vscode.Uri.joinPath(workspaceFolder.uri, filePath);
            const stat = await vscode.workspace.fs.stat(fullPath);
            return {
                size: stat.size,
                modified: new Date(stat.mtime)
            };
        }
        catch (error) {
            console.error(`Failed to get file info for ${filePath}:`, error);
            return null;
        }
    }
    async backupFile(filePath) {
        try {
            const content = await this.readFile(filePath);
            if (!content) {
                return null;
            }
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupPath = `${filePath}.backup.${timestamp}`;
            const success = await this.createFile(backupPath, content);
            return success ? backupPath : null;
        }
        catch (error) {
            console.error(`Failed to backup file ${filePath}:`, error);
            return null;
        }
    }
    getWorkspaceFolder() {
        return vscode.workspace.workspaceFolders?.[0];
    }
    async executeFileOperations(operations) {
        let allSuccessful = true;
        for (const operation of operations) {
            try {
                switch (operation.type) {
                    case 'create':
                        if (operation.content !== undefined) {
                            const success = await this.createFile(operation.filePath, operation.content);
                            allSuccessful = allSuccessful && success;
                        }
                        break;
                    case 'modify':
                        if (operation.changes) {
                            const success = await this.modifyFile(operation.filePath, operation.changes);
                            allSuccessful = allSuccessful && success;
                        }
                        break;
                    case 'delete':
                        // Implement delete operation if needed
                        break;
                }
            }
            catch (error) {
                console.error(`Failed to execute operation ${operation.type} on ${operation.filePath}:`, error);
                allSuccessful = false;
            }
        }
        return allSuccessful;
    }
}
exports.FileOperationsManager = FileOperationsManager;
//# sourceMappingURL=FileOperationsManager.js.map