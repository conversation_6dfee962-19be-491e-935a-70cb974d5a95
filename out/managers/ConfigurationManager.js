"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationManager = void 0;
const vscode = __importStar(require("vscode"));
class ConfigurationManager {
    constructor() {
        this.config = this.loadConfiguration();
    }
    loadConfiguration() {
        const config = vscode.workspace.getConfiguration('codemind-ai');
        return {
            apiProvider: config.get('apiProvider', 'anthropic'),
            apiKey: config.get('apiKey', ''),
            model: config.get('model', 'claude-3-sonnet-20240229'),
            maxTokens: config.get('maxTokens', 4000),
            temperature: config.get('temperature', 0.1)
        };
    }
    getConfiguration() {
        return { ...this.config };
    }
    refresh() {
        this.config = this.loadConfiguration();
    }
    isConfigured() {
        return this.config.apiKey.length > 0;
    }
    async promptForApiKey() {
        const apiKey = await vscode.window.showInputBox({
            prompt: `Enter your ${this.config.apiProvider.toUpperCase()} API key`,
            password: true,
            placeHolder: 'sk-...'
        });
        if (apiKey) {
            await vscode.workspace.getConfiguration('codemind-ai').update('apiKey', apiKey, vscode.ConfigurationTarget.Global);
            this.refresh();
            return true;
        }
        return false;
    }
    getModelEndpoint() {
        switch (this.config.apiProvider) {
            case 'openai':
                return 'https://api.openai.com/v1/chat/completions';
            case 'anthropic':
                return 'https://api.anthropic.com/v1/messages';
            default:
                throw new Error(`Unsupported API provider: ${this.config.apiProvider}`);
        }
    }
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        switch (this.config.apiProvider) {
            case 'openai':
                headers['Authorization'] = `Bearer ${this.config.apiKey}`;
                break;
            case 'anthropic':
                headers['x-api-key'] = this.config.apiKey;
                headers['anthropic-version'] = '2023-06-01';
                break;
        }
        return headers;
    }
    formatRequestBody(messages) {
        switch (this.config.apiProvider) {
            case 'openai':
                return {
                    model: this.config.model,
                    messages: messages,
                    max_tokens: this.config.maxTokens,
                    temperature: this.config.temperature
                };
            case 'anthropic':
                // Convert messages to Anthropic format
                const systemMessage = messages.find(m => m.role === 'system');
                const userMessages = messages.filter(m => m.role !== 'system');
                return {
                    model: this.config.model,
                    max_tokens: this.config.maxTokens,
                    temperature: this.config.temperature,
                    system: systemMessage?.content || '',
                    messages: userMessages
                };
            default:
                throw new Error(`Unsupported API provider: ${this.config.apiProvider}`);
        }
    }
    extractResponse(response) {
        switch (this.config.apiProvider) {
            case 'openai':
                return response.choices?.[0]?.message?.content || 'No response received';
            case 'anthropic':
                return response.content?.[0]?.text || 'No response received';
            default:
                throw new Error(`Unsupported API provider: ${this.config.apiProvider}`);
        }
    }
}
exports.ConfigurationManager = ConfigurationManager;
//# sourceMappingURL=ConfigurationManager.js.map