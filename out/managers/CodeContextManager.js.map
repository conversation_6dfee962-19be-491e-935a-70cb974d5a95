{"version": 3, "file": "CodeContextManager.js", "sourceRoot": "", "sources": ["../../src/managers/CodeContextManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAE7B,MAAa,kBAAkB;IAEpB,KAAK,CAAC,cAAc,CAAC,QAA6B,EAAE,SAA2B;QAClF,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,uBAAuB;QACvB,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC1D,OAAO,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QAEjD,6DAA6D;QAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAEzE,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE;YAClE,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACpC,KAAK,IAAI,CAAC,GAAG,SAAS,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,EAAE,EAAE;gBACvC,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAChC,MAAM,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;gBACtF,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;aACnD;SACJ;QAED,0CAA0C;QAC1C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;QAE7E,IAAI,gBAAgB,EAAE;YAClB,OAAO,CAAC,IAAI,CAAC,gBAAgB,gBAAgB,CAAC,IAAI,KAAK,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC;SACnF;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,QAA6B;QACrD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,uBAAuB;QACvB,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC1D,OAAO,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QACjD,OAAO,CAAC,IAAI,CAAC,UAAU,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;QAE7C,8CAA8C;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACxC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;SACpD;QAED,8CAA8C;QAC9C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACrB,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9E,CAAC,CAAC,CAAC;SACN;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEM,KAAK,CAAC,mBAAmB;QAC5B,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACnC,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAC7D,OAAO,CAAC,IAAI,CAAC,cAAc,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;YAEnD,qCAAqC;YACrC,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;YAChF,IAAI;gBACA,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;gBACvE,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAEvD,OAAO,CAAC,IAAI,CAAC,YAAY,WAAW,CAAC,IAAI,IAAI,SAAS,EAAE,CAAC,CAAC;gBAC1D,IAAI,WAAW,CAAC,WAAW,EAAE;oBACzB,OAAO,CAAC,IAAI,CAAC,gBAAgB,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC;iBAC3D;gBACD,IAAI,WAAW,CAAC,YAAY,EAAE;oBAC1B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;oBAChE,OAAO,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBACpD;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,yDAAyD;aAC5D;YAED,mBAAmB;YACnB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAChD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxB,OAAO,CAAC,IAAI,CAAC,mBAAmB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aAC7D;SACJ;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAA6B;QAC1D,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAChD,sCAAsC,EACtC,QAAQ,CAAC,GAAG,CACf,CAAC;YACF,OAAO,OAAO,IAAI,EAAE,CAAC;SACxB;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAEO,oBAAoB,CAAC,OAAgC,EAAE,QAAyB;QACpF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBACjC,gDAAgD;gBAChD,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACzE,OAAO,WAAW,IAAI,MAAM,CAAC;aAChC;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,cAAc,CAAC,QAA6B;QAChD,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QAEzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAE5C,gCAAgC;YAChC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBACtF,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtB;YACD,iBAAiB;iBACZ,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBAC7D,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtB;YACD,eAAe;iBACV,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACvD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtB;YACD,sBAAsB;iBACjB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACtD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtB;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,kBAAkB,CAAC,IAAuB;QAC9C,QAAQ,IAAI,EAAE;YACV,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,OAAO,UAAU,CAAC;YACnD,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,OAAO,OAAO,CAAC;YAC7C,KAAK,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC/C,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,OAAO,UAAU,CAAC;YACnD,KAAK,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,WAAW,CAAC;YACrD,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,MAAM,CAAC;YAC3C,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,OAAO,UAAU,CAAC;YACnD,OAAO,CAAC,CAAC,OAAO,QAAQ,CAAC;SAC5B;IACL,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,2DAA2D;QAC3D,gDAAgD;QAChD,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;YACxC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBACrB,IAAI,GAAG,CAAC,KAAK,YAAY,MAAM,CAAC,YAAY,EAAE;wBAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;wBACrD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;4BACjC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;yBAC9B;qBACJ;gBACL,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;QAED,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,4BAA4B;IAChE,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAAC,QAA6B;QAM5D,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEtE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;QAC3H,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAE/E,IAAI,UAAU,GAA8B,KAAK,CAAC;QAClD,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,IAAI,SAAS,GAAG,EAAE,IAAI,OAAO,GAAG,EAAE,EAAE;YACtD,UAAU,GAAG,MAAM,CAAC;SACvB;aAAM,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,IAAI,SAAS,GAAG,EAAE,IAAI,OAAO,GAAG,CAAC,EAAE;YAC5D,UAAU,GAAG,QAAQ,CAAC;SACzB;QAED,OAAO;YACH,WAAW,EAAE,KAAK,CAAC,MAAM;YACzB,SAAS;YACT,OAAO;YACP,UAAU;SACb,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,sBAAsB;QAC/B,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAElD,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO,YAAY,CAAC;SACvB;QAED,gCAAgC;QAChC,MAAM,WAAW,GAAG;YAChB,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,QAAQ;YAC1D,SAAS,EAAE,cAAc,EAAE,eAAe,EAAE,SAAS;SACxD,CAAC;QAEF,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;YAClC,IAAI;gBACA,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;gBACxE,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAE3C,QAAQ,UAAU,EAAE;oBAChB,KAAK,cAAc;wBACf,YAAY,CAAC,IAAI,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC;wBACtD,MAAM;oBACV,KAAK,kBAAkB;wBACnB,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAC5B,MAAM;oBACV,KAAK,YAAY;wBACb,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC1B,MAAM;oBACV,KAAK,QAAQ;wBACT,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACxB,MAAM;oBACV,KAAK,SAAS,CAAC;oBACf,KAAK,cAAc;wBACf,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC1B,MAAM;oBACV,KAAK,eAAe;wBAChB,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACzB,MAAM;oBACV,KAAK,SAAS;wBACV,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC1B,MAAM;iBACb;aACJ;YAAC,MAAM;gBACJ,+BAA+B;aAClC;SACJ;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,oBAAoB;IAC3D,CAAC;IAEO,kBAAkB;QACtB,OAAO,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;CACJ;AAtQD,gDAsQC"}