"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeContextManager = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
class CodeContextManager {
    async getCodeContext(document, selection) {
        const context = [];
        // Add file information
        context.push(`File: ${path.basename(document.fileName)}`);
        context.push(`Language: ${document.languageId}`);
        // Add surrounding context (lines before and after selection)
        const startLine = Math.max(0, selection.start.line - 5);
        const endLine = Math.min(document.lineCount - 1, selection.end.line + 5);
        if (startLine < selection.start.line || endLine > selection.end.line) {
            context.push('\nSurrounding code:');
            for (let i = startLine; i <= endLine; i++) {
                const line = document.lineAt(i);
                const prefix = i >= selection.start.line && i <= selection.end.line ? '>>> ' : '    ';
                context.push(`${prefix}${i + 1}: ${line.text}`);
            }
        }
        // Add function/class context if available
        const symbols = await this.getDocumentSymbols(document);
        const containingSymbol = this.findContainingSymbol(symbols, selection.start);
        if (containingSymbol) {
            context.push(`\nContaining ${containingSymbol.kind}: ${containingSymbol.name}`);
        }
        return context.join('\n');
    }
    async getFileContext(document) {
        const context = [];
        // Add file information
        context.push(`File: ${path.basename(document.fileName)}`);
        context.push(`Language: ${document.languageId}`);
        context.push(`Lines: ${document.lineCount}`);
        // Add imports/requires at the top of the file
        const imports = this.extractImports(document);
        if (imports.length > 0) {
            context.push('\nImports/Dependencies:');
            imports.forEach(imp => context.push(`  ${imp}`));
        }
        // Add main symbols (functions, classes, etc.)
        const symbols = await this.getDocumentSymbols(document);
        if (symbols.length > 0) {
            context.push('\nMain symbols:');
            symbols.forEach(symbol => {
                context.push(`  ${this.symbolKindToString(symbol.kind)}: ${symbol.name}`);
            });
        }
        return context.join('\n');
    }
    async getWorkspaceContext() {
        const context = [];
        if (vscode.workspace.workspaceFolders) {
            const workspaceFolder = vscode.workspace.workspaceFolders[0];
            context.push(`Workspace: ${workspaceFolder.name}`);
            // Get package.json info if available
            const packageJsonUri = vscode.Uri.joinPath(workspaceFolder.uri, 'package.json');
            try {
                const packageJson = await vscode.workspace.fs.readFile(packageJsonUri);
                const packageData = JSON.parse(packageJson.toString());
                context.push(`Project: ${packageData.name || 'Unknown'}`);
                if (packageData.description) {
                    context.push(`Description: ${packageData.description}`);
                }
                if (packageData.dependencies) {
                    const deps = Object.keys(packageData.dependencies).slice(0, 10);
                    context.push(`Dependencies: ${deps.join(', ')}`);
                }
            }
            catch (error) {
                // package.json not found or invalid, continue without it
            }
            // Get recent files
            const recentFiles = await this.getRecentFiles();
            if (recentFiles.length > 0) {
                context.push(`\nRecent files: ${recentFiles.join(', ')}`);
            }
        }
        return context.join('\n');
    }
    async getDocumentSymbols(document) {
        try {
            const symbols = await vscode.commands.executeCommand('vscode.executeDocumentSymbolProvider', document.uri);
            return symbols || [];
        }
        catch (error) {
            return [];
        }
    }
    findContainingSymbol(symbols, position) {
        for (const symbol of symbols) {
            if (symbol.range.contains(position)) {
                // Check if there's a more specific child symbol
                const childSymbol = this.findContainingSymbol(symbol.children, position);
                return childSymbol || symbol;
            }
        }
        return null;
    }
    extractImports(document) {
        const imports = [];
        const maxLinesToCheck = Math.min(50, document.lineCount);
        for (let i = 0; i < maxLinesToCheck; i++) {
            const line = document.lineAt(i).text.trim();
            // JavaScript/TypeScript imports
            if (line.startsWith('import ') || line.startsWith('const ') && line.includes('require(')) {
                imports.push(line);
            }
            // Python imports
            else if (line.startsWith('import ') || line.startsWith('from ')) {
                imports.push(line);
            }
            // Java imports
            else if (line.startsWith('import ') && line.endsWith(';')) {
                imports.push(line);
            }
            // C# using statements
            else if (line.startsWith('using ') && line.endsWith(';')) {
                imports.push(line);
            }
        }
        return imports;
    }
    symbolKindToString(kind) {
        switch (kind) {
            case vscode.SymbolKind.Function: return 'Function';
            case vscode.SymbolKind.Class: return 'Class';
            case vscode.SymbolKind.Method: return 'Method';
            case vscode.SymbolKind.Variable: return 'Variable';
            case vscode.SymbolKind.Interface: return 'Interface';
            case vscode.SymbolKind.Enum: return 'Enum';
            case vscode.SymbolKind.Constant: return 'Constant';
            default: return 'Symbol';
        }
    }
    async getRecentFiles() {
        // This is a simplified version - in a real implementation,
        // you might want to track recently opened files
        const recentFiles = [];
        if (vscode.window.tabGroups.all.length > 0) {
            vscode.window.tabGroups.all.forEach(group => {
                group.tabs.forEach(tab => {
                    if (tab.input instanceof vscode.TabInputText) {
                        const fileName = path.basename(tab.input.uri.fsPath);
                        if (!recentFiles.includes(fileName)) {
                            recentFiles.push(fileName);
                        }
                    }
                });
            });
        }
        return recentFiles.slice(0, 5); // Return max 5 recent files
    }
    async analyzeCodeComplexity(document) {
        const text = document.getText();
        const lines = text.split('\n').filter(line => line.trim().length > 0);
        const symbols = await this.getDocumentSymbols(document);
        const functions = symbols.filter(s => s.kind === vscode.SymbolKind.Function || s.kind === vscode.SymbolKind.Method).length;
        const classes = symbols.filter(s => s.kind === vscode.SymbolKind.Class).length;
        let complexity = 'low';
        if (lines.length > 500 || functions > 20 || classes > 10) {
            complexity = 'high';
        }
        else if (lines.length > 200 || functions > 10 || classes > 5) {
            complexity = 'medium';
        }
        return {
            linesOfCode: lines.length,
            functions,
            classes,
            complexity
        };
    }
    async getProjectTechnologies() {
        const technologies = [];
        const workspaceFolder = this.getWorkspaceFolder();
        if (!workspaceFolder) {
            return technologies;
        }
        // Check for common config files
        const configFiles = [
            'package.json', 'requirements.txt', 'Cargo.toml', 'go.mod',
            'pom.xml', 'build.gradle', 'composer.json', 'Gemfile'
        ];
        for (const configFile of configFiles) {
            try {
                const configPath = vscode.Uri.joinPath(workspaceFolder.uri, configFile);
                await vscode.workspace.fs.stat(configPath);
                switch (configFile) {
                    case 'package.json':
                        technologies.push('JavaScript/TypeScript', 'Node.js');
                        break;
                    case 'requirements.txt':
                        technologies.push('Python');
                        break;
                    case 'Cargo.toml':
                        technologies.push('Rust');
                        break;
                    case 'go.mod':
                        technologies.push('Go');
                        break;
                    case 'pom.xml':
                    case 'build.gradle':
                        technologies.push('Java');
                        break;
                    case 'composer.json':
                        technologies.push('PHP');
                        break;
                    case 'Gemfile':
                        technologies.push('Ruby');
                        break;
                }
            }
            catch {
                // File doesn't exist, continue
            }
        }
        return [...new Set(technologies)]; // Remove duplicates
    }
    getWorkspaceFolder() {
        return vscode.workspace.workspaceFolders?.[0];
    }
}
exports.CodeContextManager = CodeContextManager;
//# sourceMappingURL=CodeContextManager.js.map