{"version": 3, "file": "AugmentAIProvider.js", "sourceRoot": "", "sources": ["../../src/providers/AugmentAIProvider.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAmB7C,MAAa,iBAAiB;IAI1B,YAAY,aAAmC;QAFvC,wBAAmB,GAAkB,EAAE,CAAC;QAG5C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACnC,CAAC;IAEO,uBAAuB;QAC3B,MAAM,aAAa,GAAgB;YAC/B,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE;;;;;;;;;;;;;;;;;;yFAkBoE;YAC7E,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;QAEF,IAAI,CAAC,mBAAmB,GAAG,CAAC,aAAa,CAAC,CAAC;IAC/C,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,WAAmB,EAAE,OAAgB;QAC1D,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,EAAE;YACpC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC;YAC9D,IAAI,CAAC,UAAU,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;aACvE;SACJ;QAED,yCAAyC;QACzC,IAAI,eAAe,GAAG,WAAW,CAAC;QAClC,IAAI,OAAO,EAAE;YACT,eAAe,GAAG,GAAG,WAAW,iBAAiB,OAAO,EAAE,CAAC;SAC9D;QAED,8BAA8B;QAC9B,MAAM,eAAe,GAAgB;YACjC,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,eAAe;YACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;QACF,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAE/C,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAE7C,oCAAoC;YACpC,MAAM,gBAAgB,GAAgB;gBAClC,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC;YACF,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEhD,OAAO,QAAQ,CAAC;SACnB;QAAC,OAAO,KAAK,EAAE;YACZ,6DAA6D;YAC7D,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC;YAC/B,MAAM,KAAK,CAAC;SACf;IACL,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;QAEhD,uEAAuE;QACvE,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC,CAAC,aAAa;QAEtE,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAEzE,IAAI;YACA,MAAM,QAAQ,GAAkB,MAAM,eAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE;gBACpE,OAAO;gBACP,OAAO,EAAE,KAAK,CAAC,oBAAoB;aACtC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAElE,OAAO;gBACH,OAAO;gBACP,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;aAC1C,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;gBAC3B,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE;oBAChC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;iBACxE;qBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE;oBACvC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;iBACnE;qBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE;oBACvC,MAAM,IAAI,KAAK,CAAC,gBAAgB,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,iBAAiB,EAAE,CAAC,CAAC;iBAC/F;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE;oBACtC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;iBACzD;qBAAM;oBACH,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;iBAC3D;aACJ;iBAAM;gBACH,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;aACjD;SACJ;IACL,CAAC;IAEO,YAAY,CAAC,YAAiB;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;QAErD,QAAQ,MAAM,CAAC,WAAW,EAAE;YACxB,KAAK,QAAQ;gBACT,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;oBACxB,YAAY,EAAE,YAAY,CAAC,KAAK,CAAC,aAAa;oBAC9C,gBAAgB,EAAE,YAAY,CAAC,KAAK,CAAC,iBAAiB;oBACtD,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC,YAAY;iBAC/C,CAAC,CAAC,CAAC,SAAS,CAAC;YAClB,KAAK,WAAW;gBACZ,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;oBACxB,YAAY,EAAE,YAAY,CAAC,KAAK,CAAC,YAAY;oBAC7C,gBAAgB,EAAE,YAAY,CAAC,KAAK,CAAC,aAAa;oBAClD,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,aAAa;iBAClF,CAAC,CAAC,CAAC,SAAS,CAAC;YAClB;gBACI,OAAO,SAAS,CAAC;SACxB;IACL,CAAC;IAEM,sBAAsB;QACzB,OAAO,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,gBAAgB;IAC1D,CAAC;IAEM,iBAAiB;QACpB,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACnC,CAAC;IAEM,mBAAmB;QACtB,gEAAgE;QAChE,4DAA4D;QAC5D,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,cAAc;QACvB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI;YACA,MAAM,WAAW,GAAG,qEAAqE,CAAC;YAC1F,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACrD,OAAO,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;SAC3E;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAEM,aAAa;QAChB,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACtD,2CAA2C;YAC3C,OAAO,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC;IAEM,uBAAuB,CAAC,YAAoB,IAAI;QACnD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAE3C,IAAI,aAAa,IAAI,SAAS,EAAE;YAC5B,OAAO;SACV;QAED,6DAA6D;QAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAClD,IAAI,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAE1D,OAAO,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,GAAG,SAAS,EAAE;YACrE,4CAA4C;YAC5C,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,mBAAmB,GAAG,CAAC,aAAa,EAAE,GAAG,iBAAiB,CAAC,CAAC;IACrE,CAAC;CACJ;AAlMD,8CAkMC"}