"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AugmentAIProvider = void 0;
const axios_1 = __importDefault(require("axios"));
class AugmentAIProvider {
    constructor(configManager) {
        this.conversationHistory = [];
        this.configManager = configManager;
        this.initializeSystemMessage();
    }
    initializeSystemMessage() {
        const systemMessage = {
            role: 'system',
            content: `You are CodeMind AI Assistant, an intelligent AI-powered coding companion. You are an expert programmer who helps with:

1. Code explanation and analysis
2. Code generation and completion
3. Refactoring and optimization
4. Debugging assistance
5. Best practices and recommendations
6. Documentation generation

IMPORTANT GUIDELINES:
- Always provide working, tested code
- Include comments explaining complex logic
- Follow language-specific best practices
- Consider performance and security
- Be concise but thorough
- When generating code, make it production-ready
- If you're unsure, ask clarifying questions

You have access to the current workspace context. Focus on practical, working solutions.`,
            timestamp: Date.now()
        };
        this.conversationHistory = [systemMessage];
    }
    async sendMessage(userMessage, context) {
        if (!this.configManager.isConfigured()) {
            const configured = await this.configManager.promptForApiKey();
            if (!configured) {
                throw new Error('API key is required to use CodeMind AI Assistant');
            }
        }
        // Add context to the message if provided
        let enhancedMessage = userMessage;
        if (context) {
            enhancedMessage = `${userMessage}\n\nContext:\n${context}`;
        }
        // Add user message to history
        const userChatMessage = {
            role: 'user',
            content: enhancedMessage,
            timestamp: Date.now()
        };
        this.conversationHistory.push(userChatMessage);
        try {
            const response = await this.makeAPIRequest();
            // Add assistant response to history
            const assistantMessage = {
                role: 'assistant',
                content: response.content,
                timestamp: Date.now()
            };
            this.conversationHistory.push(assistantMessage);
            return response;
        }
        catch (error) {
            // Remove the user message from history if the request failed
            this.conversationHistory.pop();
            throw error;
        }
    }
    async makeAPIRequest() {
        const config = this.configManager.getConfiguration();
        const endpoint = this.configManager.getModelEndpoint();
        const headers = this.configManager.getHeaders();
        // Prepare messages for API (exclude system message for some providers)
        const messagesToSend = this.conversationHistory.slice(); // Copy array
        const requestBody = this.configManager.formatRequestBody(messagesToSend);
        try {
            const response = await axios_1.default.post(endpoint, requestBody, {
                headers,
                timeout: 30000 // 30 second timeout
            });
            const content = this.configManager.extractResponse(response.data);
            return {
                content,
                usage: this.extractUsage(response.data)
            };
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                if (error.response?.status === 401) {
                    throw new Error('Invalid API key. Please check your configuration.');
                }
                else if (error.response?.status === 429) {
                    throw new Error('Rate limit exceeded. Please try again later.');
                }
                else if (error.response?.status === 400) {
                    throw new Error(`Bad request: ${error.response.data?.error?.message || 'Invalid request'}`);
                }
                else if (error.code === 'ECONNABORTED') {
                    throw new Error('Request timeout. Please try again.');
                }
                else {
                    throw new Error(`API request failed: ${error.message}`);
                }
            }
            else {
                throw new Error(`Unexpected error: ${error}`);
            }
        }
    }
    extractUsage(responseData) {
        const config = this.configManager.getConfiguration();
        switch (config.apiProvider) {
            case 'openai':
                return responseData.usage ? {
                    promptTokens: responseData.usage.prompt_tokens,
                    completionTokens: responseData.usage.completion_tokens,
                    totalTokens: responseData.usage.total_tokens
                } : undefined;
            case 'anthropic':
                return responseData.usage ? {
                    promptTokens: responseData.usage.input_tokens,
                    completionTokens: responseData.usage.output_tokens,
                    totalTokens: responseData.usage.input_tokens + responseData.usage.output_tokens
                } : undefined;
            default:
                return undefined;
        }
    }
    getConversationHistory() {
        return [...this.conversationHistory]; // Return a copy
    }
    clearConversation() {
        this.initializeSystemMessage();
    }
    updateConfiguration() {
        // Configuration has been updated, we might need to reinitialize
        // For now, we'll just clear the conversation to start fresh
        this.clearConversation();
    }
    async testConnection() {
        if (!this.configManager.isConfigured()) {
            return false;
        }
        try {
            const testMessage = "Hello! Please respond with 'Connection successful' to test the API.";
            const response = await this.sendMessage(testMessage);
            return response.content.toLowerCase().includes('connection successful');
        }
        catch (error) {
            console.error('Connection test failed:', error);
            return false;
        }
    }
    getTokenCount() {
        return this.conversationHistory.reduce((total, message) => {
            // Rough estimation: 1 token ≈ 4 characters
            return total + Math.ceil(message.content.length / 4);
        }, 0);
    }
    trimConversationHistory(maxTokens = 8000) {
        const currentTokens = this.getTokenCount();
        if (currentTokens <= maxTokens) {
            return;
        }
        // Keep system message and remove oldest user/assistant pairs
        const systemMessage = this.conversationHistory[0];
        let remainingMessages = this.conversationHistory.slice(1);
        while (remainingMessages.length > 0 && this.getTokenCount() > maxTokens) {
            // Remove the oldest pair (user + assistant)
            remainingMessages = remainingMessages.slice(2);
        }
        this.conversationHistory = [systemMessage, ...remainingMessages];
    }
}
exports.AugmentAIProvider = AugmentAIProvider;
//# sourceMappingURL=AugmentAIProvider.js.map