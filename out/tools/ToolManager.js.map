{"version": 3, "file": "ToolManager.js", "sourceRoot": "", "sources": ["../../src/tools/ToolManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,6EAA0E;AAc1E,MAAa,WAAW;IAIpB;QAHQ,UAAK,GAAsB,IAAI,GAAG,EAAE,CAAC;QAIzC,IAAI,CAAC,cAAc,GAAG,IAAI,6CAAqB,EAAE,CAAC;QAClD,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,oBAAoB;QACxB,wBAAwB;QACxB,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE,8CAA8C;YAC3D,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,4BAA4B,EAAE;oBACvE,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,qBAAqB,EAAE;iBAClE;gBACD,QAAQ,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;aACpC;YACD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACtB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YACjF,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,4BAA4B;YACzC,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE;iBACxE;gBACD,QAAQ,EAAE,CAAC,UAAU,CAAC;aACzB;YACD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACtB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC/D,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,2BAA2B;YACxC,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,8BAA8B,EAAE,OAAO,EAAE,EAAE,EAAE;oBACvF,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,wCAAwC,EAAE;iBACrF;aACJ;YACD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACtB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YACjF,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,EAAE,cAAc;YACpB,WAAW,EAAE,0BAA0B;YACvC,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oBAAoB,EAAE;oBACjE,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,uCAAuC,EAAE;iBACxF;gBACD,QAAQ,EAAE,CAAC,YAAY,CAAC;aAC3B;YACD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACtB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;YAC1F,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,EAAE,kBAAkB;YACxB,WAAW,EAAE,+CAA+C;YAC5D,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,mBAAmB,EAAE;iBAChE;gBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;aACxB;YACD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACtB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACpE,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,EAAE,mBAAmB;YACzB,WAAW,EAAE,qCAAqC;YAClD,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,mCAAmC,EAAE;iBAChF;gBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;aACxB;YACD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACtB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACtE,CAAC;SACJ,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,mCAAmC;YAChD,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oBAAoB,EAAE;oBAC9D,gBAAgB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,mCAAmC,EAAE;iBACzF;gBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;aACxB;YACD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACtB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAC;YAClF,CAAC;SACJ,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,2BAA2B;YACxC,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE;iBACxE;gBACD,QAAQ,EAAE,CAAC,UAAU,CAAC;aACzB;YACD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACtB,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAChD,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,EAAE,cAAc;YACpB,WAAW,EAAE,4BAA4B;YACzC,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACR,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE;oBAC3D,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE;iBAChF;gBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;aACxB;YACD,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;gBACtB,QAAQ,MAAM,CAAC,IAAI,EAAE;oBACjB,KAAK,SAAS;wBACV,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;wBACjD,MAAM;oBACV,KAAK,OAAO;wBACR,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;wBAC/C,MAAM;oBACV;wBACI,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;iBAC5D;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;SACJ,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE,6CAA6C;YAC1D,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,EAAE;aACjB;YACD,OAAO,EAAE,KAAK,IAAI,EAAE;gBAChB,OAAO,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzC,CAAC;SACJ,CAAC,CAAC;IACP,CAAC;IAEM,YAAY,CAAC,IAAU;QAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAEM,iBAAiB;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,QAAkB;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,IAAI,EAAE;YACP,MAAM,IAAI,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAI,aAAa,CAAC,CAAC;SACxD;QAED,IAAI;YACA,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAClD;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;SACtD;IACL,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAAC,SAAqB;QACnD,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;YAC9B,IAAI;gBACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAChD,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;aACpE;YAAC,OAAO,KAAK,EAAE;gBACZ,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5E,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;aAClF;SACJ;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAe,EAAE,gBAAyB;QACvE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC1C,IAAI,EAAE,oBAAoB;gBAC1B,GAAG,EAAE,gBAAgB;aACxB,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChB,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE3B,wEAAwE;YACxE,iDAAiD;YACjD,OAAO,CAAC,YAAY,OAAO,wBAAwB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,QAAgB;QACnC,IAAI;YACA,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,eAAe,EAAE;gBAClB,OAAO,KAAK,CAAC;aAChB;YAED,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YACpE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC;SACf;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,uBAAuB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC1B,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC;SACjD;QAED,MAAM,IAAI,GAAQ;YACd,IAAI,EAAE,eAAe,CAAC,IAAI;YAC1B,IAAI,EAAE,eAAe,CAAC,GAAG,CAAC,MAAM;YAChC,SAAS,EAAE,EAAE;SAChB,CAAC;QAEF,iBAAiB;QACjB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACrB,IAAI,GAAG,CAAC,KAAK,YAAY,MAAM,CAAC,YAAY,EAAE;oBAC1C,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACpE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBACrC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI;YACA,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;YACjF,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YAC/E,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9D,IAAI,CAAC,WAAW,GAAG;gBACf,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC;gBACzD,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,IAAI,EAAE,CAAC;aAClE,CAAC;SACL;QAAC,MAAM;YACJ,kCAAkC;SACrC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvC,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;SAC9B,CAAC,CAAC,CAAC;IACR,CAAC;CACJ;AArSD,kCAqSC"}