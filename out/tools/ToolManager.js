"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolManager = void 0;
const vscode = __importStar(require("vscode"));
const FileOperationsManager_1 = require("../managers/FileOperationsManager");
class ToolManager {
    constructor() {
        this.tools = new Map();
        this.fileOpsManager = new FileOperationsManager_1.FileOperationsManager();
        this.registerBuiltInTools();
    }
    registerBuiltInTools() {
        // File operations tools
        this.registerTool({
            name: 'create_file',
            description: 'Create a new file with the specified content',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to create' },
                    content: { type: 'string', description: 'Content of the file' }
                },
                required: ['filePath', 'content']
            },
            execute: async (params) => {
                return await this.fileOpsManager.createFile(params.filePath, params.content);
            }
        });
        this.registerTool({
            name: 'read_file',
            description: 'Read the content of a file',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to read' }
                },
                required: ['filePath']
            },
            execute: async (params) => {
                return await this.fileOpsManager.readFile(params.filePath);
            }
        });
        this.registerTool({
            name: 'list_files',
            description: 'List files in a directory',
            parameters: {
                type: 'object',
                properties: {
                    directory: { type: 'string', description: 'Directory to list files from', default: '' },
                    pattern: { type: 'string', description: 'Optional regex pattern to filter files' }
                }
            },
            execute: async (params) => {
                return await this.fileOpsManager.listFiles(params.directory, params.pattern);
            }
        });
        this.registerTool({
            name: 'search_files',
            description: 'Search for text in files',
            parameters: {
                type: 'object',
                properties: {
                    searchTerm: { type: 'string', description: 'Text to search for' },
                    filePattern: { type: 'string', description: 'Optional file pattern to limit search' }
                },
                required: ['searchTerm']
            },
            execute: async (params) => {
                return await this.fileOpsManager.searchInFiles(params.searchTerm, params.filePattern);
            }
        });
        this.registerTool({
            name: 'insert_at_cursor',
            description: 'Insert content at the current cursor position',
            parameters: {
                type: 'object',
                properties: {
                    content: { type: 'string', description: 'Content to insert' }
                },
                required: ['content']
            },
            execute: async (params) => {
                return await this.fileOpsManager.insertAtCursor(params.content);
            }
        });
        this.registerTool({
            name: 'replace_selection',
            description: 'Replace the currently selected text',
            parameters: {
                type: 'object',
                properties: {
                    content: { type: 'string', description: 'Content to replace selection with' }
                },
                required: ['content']
            },
            execute: async (params) => {
                return await this.fileOpsManager.replaceSelection(params.content);
            }
        });
        // Terminal operations
        this.registerTool({
            name: 'run_terminal_command',
            description: 'Execute a command in the terminal',
            parameters: {
                type: 'object',
                properties: {
                    command: { type: 'string', description: 'Command to execute' },
                    workingDirectory: { type: 'string', description: 'Working directory for the command' }
                },
                required: ['command']
            },
            execute: async (params) => {
                return await this.runTerminalCommand(params.command, params.workingDirectory);
            }
        });
        // VS Code operations
        this.registerTool({
            name: 'open_file',
            description: 'Open a file in the editor',
            parameters: {
                type: 'object',
                properties: {
                    filePath: { type: 'string', description: 'Path to the file to open' }
                },
                required: ['filePath']
            },
            execute: async (params) => {
                return await this.openFile(params.filePath);
            }
        });
        this.registerTool({
            name: 'show_message',
            description: 'Show a message to the user',
            parameters: {
                type: 'object',
                properties: {
                    message: { type: 'string', description: 'Message to show' },
                    type: { type: 'string', enum: ['info', 'warning', 'error'], default: 'info' }
                },
                required: ['message']
            },
            execute: async (params) => {
                switch (params.type) {
                    case 'warning':
                        vscode.window.showWarningMessage(params.message);
                        break;
                    case 'error':
                        vscode.window.showErrorMessage(params.message);
                        break;
                    default:
                        vscode.window.showInformationMessage(params.message);
                }
                return true;
            }
        });
        // Workspace operations
        this.registerTool({
            name: 'get_workspace_info',
            description: 'Get information about the current workspace',
            parameters: {
                type: 'object',
                properties: {}
            },
            execute: async () => {
                return await this.getWorkspaceInfo();
            }
        });
    }
    registerTool(tool) {
        this.tools.set(tool.name, tool);
    }
    getAvailableTools() {
        return Array.from(this.tools.values());
    }
    async executeTool(toolCall) {
        const tool = this.tools.get(toolCall.name);
        if (!tool) {
            throw new Error(`Tool '${toolCall.name}' not found`);
        }
        try {
            return await tool.execute(toolCall.parameters);
        }
        catch (error) {
            throw new Error(`Tool execution failed: ${error}`);
        }
    }
    async executeMultipleTools(toolCalls) {
        const results = [];
        for (const toolCall of toolCalls) {
            try {
                const result = await this.executeTool(toolCall);
                results.push({ success: true, result, toolName: toolCall.name });
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                results.push({ success: false, error: errorMessage, toolName: toolCall.name });
            }
        }
        return results;
    }
    async runTerminalCommand(command, workingDirectory) {
        return new Promise((resolve, reject) => {
            const terminal = vscode.window.createTerminal({
                name: 'Augment AI Command',
                cwd: workingDirectory
            });
            terminal.show();
            terminal.sendText(command);
            // Note: VS Code doesn't provide a direct way to capture terminal output
            // This is a limitation we'll need to work around
            resolve(`Command '${command}' executed in terminal`);
        });
    }
    async openFile(filePath) {
        try {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                return false;
            }
            const fullPath = vscode.Uri.joinPath(workspaceFolder.uri, filePath);
            const document = await vscode.workspace.openTextDocument(fullPath);
            await vscode.window.showTextDocument(document);
            return true;
        }
        catch (error) {
            console.error(`Failed to open file ${filePath}:`, error);
            return false;
        }
    }
    async getWorkspaceInfo() {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return { error: 'No workspace folder found' };
        }
        const info = {
            name: workspaceFolder.name,
            path: workspaceFolder.uri.fsPath,
            openFiles: []
        };
        // Get open files
        vscode.window.tabGroups.all.forEach(group => {
            group.tabs.forEach(tab => {
                if (tab.input instanceof vscode.TabInputText) {
                    const relativePath = vscode.workspace.asRelativePath(tab.input.uri);
                    info.openFiles.push(relativePath);
                }
            });
        });
        // Try to get package.json info
        try {
            const packageJsonPath = vscode.Uri.joinPath(workspaceFolder.uri, 'package.json');
            const packageJsonContent = await vscode.workspace.fs.readFile(packageJsonPath);
            const packageJson = JSON.parse(packageJsonContent.toString());
            info.packageInfo = {
                name: packageJson.name,
                version: packageJson.version,
                description: packageJson.description,
                dependencies: Object.keys(packageJson.dependencies || {}),
                devDependencies: Object.keys(packageJson.devDependencies || {})
            };
        }
        catch {
            // No package.json or invalid JSON
        }
        return info;
    }
    getToolsSchema() {
        const tools = this.getAvailableTools();
        return tools.map(tool => ({
            name: tool.name,
            description: tool.description,
            parameters: tool.parameters
        }));
    }
}
exports.ToolManager = ToolManager;
//# sourceMappingURL=ToolManager.js.map