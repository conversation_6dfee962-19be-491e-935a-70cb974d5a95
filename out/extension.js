"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const AugmentAIProvider_1 = require("./providers/AugmentAIProvider");
const ChatWebviewProvider_1 = require("./providers/ChatWebviewProvider");
const CodeContextManager_1 = require("./managers/CodeContextManager");
const ConfigurationManager_1 = require("./managers/ConfigurationManager");
let augmentAIProvider;
let chatWebviewProvider;
let codeContextManager;
let configurationManager;
function activate(context) {
    console.log('CodeMind AI Assistant is now active!');
    // Initialize managers and providers
    configurationManager = new ConfigurationManager_1.ConfigurationManager();
    codeContextManager = new CodeContextManager_1.CodeContextManager();
    augmentAIProvider = new AugmentAIProvider_1.AugmentAIProvider(configurationManager);
    chatWebviewProvider = new ChatWebviewProvider_1.ChatWebviewProvider(context, augmentAIProvider, codeContextManager);
    // Register webview provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('codemind-ai.chatView', chatWebviewProvider, {
        webviewOptions: {
            retainContextWhenHidden: true
        }
    }));
    // Register commands
    const openChatCommand = vscode.commands.registerCommand('codemind-ai.openChat', () => {
        chatWebviewProvider.show();
    });
    const explainCodeCommand = vscode.commands.registerCommand('codemind-ai.explainCode', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);
        if (!selectedText) {
            vscode.window.showErrorMessage('No code selected');
            return;
        }
        const context = await codeContextManager.getCodeContext(editor.document, selection);
        const prompt = `Please explain this code:\n\n${selectedText}\n\nContext: ${context}`;
        chatWebviewProvider.sendMessage(prompt);
        chatWebviewProvider.show();
    });
    const generateCodeCommand = vscode.commands.registerCommand('codemind-ai.generateCode', async () => {
        const input = await vscode.window.showInputBox({
            prompt: 'Describe the code you want to generate',
            placeHolder: 'e.g., Create a function that sorts an array of objects by name'
        });
        if (!input) {
            return;
        }
        const editor = vscode.window.activeTextEditor;
        let context = '';
        if (editor) {
            context = await codeContextManager.getFileContext(editor.document);
        }
        const prompt = `Generate code for: ${input}\n\nCurrent file context: ${context}`;
        chatWebviewProvider.sendMessage(prompt);
        chatWebviewProvider.show();
    });
    const refactorCodeCommand = vscode.commands.registerCommand('codemind-ai.refactorCode', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);
        if (!selectedText) {
            vscode.window.showErrorMessage('No code selected');
            return;
        }
        const refactorType = await vscode.window.showQuickPick([
            'Improve readability',
            'Optimize performance',
            'Add error handling',
            'Extract functions',
            'Add comments',
            'Custom refactoring'
        ], {
            placeHolder: 'Select refactoring type'
        });
        if (!refactorType) {
            return;
        }
        let prompt = `Please refactor this code to ${refactorType.toLowerCase()}:\n\n${selectedText}`;
        if (refactorType === 'Custom refactoring') {
            const customInstruction = await vscode.window.showInputBox({
                prompt: 'Describe how you want to refactor the code',
                placeHolder: 'e.g., Convert to async/await, use modern ES6 syntax'
            });
            if (!customInstruction) {
                return;
            }
            prompt = `Please refactor this code: ${customInstruction}\n\n${selectedText}`;
        }
        const context = await codeContextManager.getCodeContext(editor.document, selection);
        prompt += `\n\nContext: ${context}`;
        chatWebviewProvider.sendMessage(prompt);
        chatWebviewProvider.show();
    });
    // Add commands to subscriptions
    context.subscriptions.push(openChatCommand, explainCodeCommand, generateCodeCommand, refactorCodeCommand);
    // Listen for configuration changes
    context.subscriptions.push(vscode.workspace.onDidChangeConfiguration(e => {
        if (e.affectsConfiguration('codemind-ai')) {
            configurationManager.refresh();
            augmentAIProvider.updateConfiguration();
        }
    }));
    // Show welcome message on first activation
    const hasShownWelcome = context.globalState.get('codemind-ai.hasShownWelcome', false);
    if (!hasShownWelcome) {
        vscode.window.showInformationMessage('Welcome to CodeMind AI Assistant! Click "Open Chat" to get started.', 'Open Chat', 'Configure API Key').then(selection => {
            if (selection === 'Open Chat') {
                vscode.commands.executeCommand('codemind-ai.openChat');
            }
            else if (selection === 'Configure API Key') {
                vscode.commands.executeCommand('workbench.action.openSettings', 'codemind-ai.apiKey');
            }
        });
        context.globalState.update('codemind-ai.hasShownWelcome', true);
    }
}
exports.activate = activate;
function deactivate() {
    console.log('CodeMind AI Assistant is now deactivated');
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map