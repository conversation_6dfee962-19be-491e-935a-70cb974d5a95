import * as vscode from 'vscode';
import { AugmentAIProvider } from './providers/AugmentAIProvider';
import { ChatWebviewProvider } from './providers/ChatWebviewProvider';
import { CodeContextManager } from './managers/CodeContextManager';
import { ConfigurationManager } from './managers/ConfigurationManager';

let augmentAIProvider: AugmentAIProvider;
let chatWebviewProvider: ChatWebviewProvider;
let codeContextManager: CodeContextManager;
let configurationManager: ConfigurationManager;

export function activate(context: vscode.ExtensionContext) {
    console.log('🚀 CodeMind AI Assistant is now active!');
    vscode.window.showInformationMessage('🚀 CodeMind AI Assistant activated!');

    try {
        // Initialize managers and providers
        console.log('📦 Initializing ConfigurationManager...');
        configurationManager = new ConfigurationManager();

        console.log('📦 Initializing CodeContextManager...');
        codeContextManager = new CodeContextManager();

        console.log('📦 Initializing AugmentAIProvider...');
        augmentAIProvider = new AugmentAIProvider(configurationManager);

        console.log('📦 Initializing ChatWebviewProvider...');
        chatWebviewProvider = new ChatWebviewProvider(context, augmentAIProvider, codeContextManager);

        console.log('✅ All providers initialized successfully!');

        // Register webview provider
        console.log('📝 Registering webview provider with ID: codemind-ai.chatView');
        try {
            const webviewProvider = vscode.window.registerWebviewViewProvider(
                'codemind-ai.chatView',
                chatWebviewProvider,
                {
                    webviewOptions: {
                        retainContextWhenHidden: true
                    }
                }
            );
            context.subscriptions.push(webviewProvider);
            console.log('✅ Webview provider registered successfully');
        } catch (error) {
            console.error('❌ Failed to register webview provider:', error);
            throw error;
        }

        // Register commands
        console.log('📝 Registering commands...');
    const openChatCommand = vscode.commands.registerCommand('codemind-ai.openChat', () => {
        console.log('🎯 Open Chat command executed!');
        chatWebviewProvider.show();
    });

    const explainCodeCommand = vscode.commands.registerCommand('codemind-ai.explainCode', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);
        
        if (!selectedText) {
            vscode.window.showErrorMessage('No code selected');
            return;
        }

        const context = await codeContextManager.getCodeContext(editor.document, selection);
        const prompt = `Please explain this code:\n\n${selectedText}\n\nContext: ${context}`;
        
        chatWebviewProvider.sendMessage(prompt);
        chatWebviewProvider.show();
    });

    const generateCodeCommand = vscode.commands.registerCommand('codemind-ai.generateCode', async () => {
        const input = await vscode.window.showInputBox({
            prompt: 'Describe the code you want to generate',
            placeHolder: 'e.g., Create a function that sorts an array of objects by name'
        });

        if (!input) {
            return;
        }

        const editor = vscode.window.activeTextEditor;
        let context = '';
        
        if (editor) {
            context = await codeContextManager.getFileContext(editor.document);
        }

        const prompt = `Generate code for: ${input}\n\nCurrent file context: ${context}`;
        
        chatWebviewProvider.sendMessage(prompt);
        chatWebviewProvider.show();
    });

    const refactorCodeCommand = vscode.commands.registerCommand('codemind-ai.refactorCode', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);
        
        if (!selectedText) {
            vscode.window.showErrorMessage('No code selected');
            return;
        }

        const refactorType = await vscode.window.showQuickPick([
            'Improve readability',
            'Optimize performance',
            'Add error handling',
            'Extract functions',
            'Add comments',
            'Custom refactoring'
        ], {
            placeHolder: 'Select refactoring type'
        });

        if (!refactorType) {
            return;
        }

        let prompt = `Please refactor this code to ${refactorType.toLowerCase()}:\n\n${selectedText}`;
        
        if (refactorType === 'Custom refactoring') {
            const customInstruction = await vscode.window.showInputBox({
                prompt: 'Describe how you want to refactor the code',
                placeHolder: 'e.g., Convert to async/await, use modern ES6 syntax'
            });
            
            if (!customInstruction) {
                return;
            }
            
            prompt = `Please refactor this code: ${customInstruction}\n\n${selectedText}`;
        }

        const context = await codeContextManager.getCodeContext(editor.document, selection);
        prompt += `\n\nContext: ${context}`;
        
        chatWebviewProvider.sendMessage(prompt);
        chatWebviewProvider.show();
    });

    // Add commands to subscriptions
    context.subscriptions.push(
        openChatCommand,
        explainCodeCommand,
        generateCodeCommand,
        refactorCodeCommand
    );

    // Listen for configuration changes
    context.subscriptions.push(
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('codemind-ai')) {
                configurationManager.refresh();
                augmentAIProvider.updateConfiguration();
            }
        })
    );

    // Show welcome message on first activation
    const hasShownWelcome = context.globalState.get('codemind-ai.hasShownWelcome', false);
    if (!hasShownWelcome) {
        vscode.window.showInformationMessage(
            'Welcome to CodeMind AI Assistant! Click "Open Chat" to get started.',
            'Open Chat',
            'Configure API Key'
        ).then(selection => {
            if (selection === 'Open Chat') {
                vscode.commands.executeCommand('codemind-ai.openChat');
            } else if (selection === 'Configure API Key') {
                vscode.commands.executeCommand('workbench.action.openSettings', 'codemind-ai.apiKey');
            }
        });
        context.globalState.update('codemind-ai.hasShownWelcome', true);
        }

        console.log('🎉 Extension activation completed successfully!');

        // Check if the view container is properly registered
        setTimeout(() => {
            console.log('🔍 Checking view container registration...');
            Promise.resolve(vscode.commands.executeCommand('workbench.view.extension.codemind-ai')).then(() => {
                console.log('✅ View container is accessible');
            }).catch((error: any) => {
                console.error('❌ View container not accessible:', error);
            });
        }, 1000);

    } catch (error) {
        console.error('❌ Extension activation failed:', error);
        vscode.window.showErrorMessage(`CodeMind AI Assistant failed to activate: ${error instanceof Error ? error.message : 'Unknown error'}`);

        // Register a minimal command so the extension doesn't completely fail
        const fallbackCommand = vscode.commands.registerCommand('codemind-ai.openChat', () => {
            vscode.window.showErrorMessage('CodeMind AI Assistant is not properly initialized. Please check the console for errors.');
        });
        context.subscriptions.push(fallbackCommand);
    }
}

export function deactivate() {
    console.log('CodeMind AI Assistant is now deactivated');
}
