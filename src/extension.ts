import * as vscode from 'vscode';
import { AugmentAIProvider } from './providers/AugmentAIProvider';
import { ConfigurationManager } from './managers/ConfigurationManager';

class ChatWebviewProvider implements vscode.WebviewViewProvider {
    private _view?: vscode.WebviewView;
    private aiProvider: AugmentAIProvider;

    constructor(aiProvider: AugmentAIProvider) {
        this.aiProvider = aiProvider;
    }

    resolveWebviewView(webviewView: vscode.WebviewView) {
        console.log('🎉 WEBVIEW RESOLVED!!!');
        
        this._view = webviewView;
        
        webviewView.webview.options = {
            enableScripts: true
        };

        webviewView.webview.html = this.getHtml();

        // Handle messages from webview
        webviewView.webview.onDidReceiveMessage(async (data) => {
            if (data.type === 'sendMessage') {
                await this.handleMessage(data.message);
            }
        });
    }

    private async handleMessage(message: string) {
        if (!this._view) return;

        // Add user message
        this._view.webview.postMessage({
            type: 'addMessage',
            role: 'user',
            content: message
        });

        try {
            // Get AI response
            const response = await this.aiProvider.sendMessage(message, '');
            
            // Add AI response
            this._view.webview.postMessage({
                type: 'addMessage',
                role: 'assistant',
                content: response.content
            });
        } catch (error) {
            this._view.webview.postMessage({
                type: 'addMessage',
                role: 'error',
                content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
        }
    }

    private getHtml(): string {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>CodeMind AI Chat</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        padding: 10px;
                        color: var(--vscode-foreground);
                        background: var(--vscode-editor-background);
                        margin: 0;
                        height: 100vh;
                        display: flex;
                        flex-direction: column;
                    }
                    #messages {
                        flex: 1;
                        overflow-y: auto;
                        margin-bottom: 10px;
                        padding: 10px;
                        border: 1px solid var(--vscode-panel-border);
                        border-radius: 4px;
                    }
                    .message {
                        margin: 10px 0;
                        padding: 8px;
                        border-radius: 4px;
                    }
                    .user {
                        background: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        text-align: right;
                    }
                    .assistant {
                        background: var(--vscode-editor-selectionBackground);
                    }
                    .error {
                        background: var(--vscode-errorBackground);
                        color: var(--vscode-errorForeground);
                    }
                    #input-container {
                        display: flex;
                        gap: 10px;
                    }
                    #messageInput {
                        flex: 1;
                        padding: 8px;
                        background: var(--vscode-input-background);
                        color: var(--vscode-input-foreground);
                        border: 1px solid var(--vscode-input-border);
                        border-radius: 4px;
                    }
                    #sendButton {
                        padding: 8px 16px;
                        background: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    }
                </style>
            </head>
            <body>
                <div id="messages">
                    <div class="message assistant">Welcome! I'm your AI coding assistant. How can I help you?</div>
                </div>
                <div id="input-container">
                    <input type="text" id="messageInput" placeholder="Ask me anything..." />
                    <button id="sendButton">Send</button>
                </div>

                <script>
                    const vscode = acquireVsCodeApi();
                    const messagesDiv = document.getElementById('messages');
                    const messageInput = document.getElementById('messageInput');
                    const sendButton = document.getElementById('sendButton');

                    function sendMessage() {
                        const message = messageInput.value.trim();
                        if (!message) return;

                        vscode.postMessage({
                            type: 'sendMessage',
                            message: message
                        });

                        messageInput.value = '';
                    }

                    sendButton.addEventListener('click', sendMessage);
                    messageInput.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            sendMessage();
                        }
                    });

                    // Handle messages from extension
                    window.addEventListener('message', (event) => {
                        const message = event.data;
                        if (message.type === 'addMessage') {
                            const messageDiv = document.createElement('div');
                            messageDiv.className = 'message ' + message.role;
                            messageDiv.textContent = message.content;
                            messagesDiv.appendChild(messageDiv);
                            messagesDiv.scrollTop = messagesDiv.scrollHeight;
                        }
                    });
                </script>
            </body>
            </html>
        `;
    }
}

export function activate(context: vscode.ExtensionContext) {
    console.log('🚀 CodeMind AI Extension activating...');
    
    // Initialize AI provider
    const configManager = new ConfigurationManager();
    const aiProvider = new AugmentAIProvider(configManager);
    
    // Create and register webview provider
    const chatProvider = new ChatWebviewProvider(aiProvider);
    vscode.window.registerWebviewViewProvider('codemind-ai.chatView', chatProvider);
    
    // Register command
    const openChatCommand = vscode.commands.registerCommand('codemind-ai.openChat', () => {
        vscode.commands.executeCommand('workbench.view.extension.codemind-ai');
    });
    
    context.subscriptions.push(openChatCommand);
    
    console.log('✅ CodeMind AI Extension activated!');
}

export function deactivate() {}
