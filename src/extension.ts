import * as vscode from 'vscode';

// Simple webview provider
class ChatWebviewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'codemind-ai.chatView';
    private _view?: vscode.WebviewView;

    constructor(private readonly _extensionUri: vscode.Uri) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        console.log('🎉 WEBVIEW RESOLVED! ChatWebviewProvider.resolveWebviewView called!');

        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview();

        webviewView.webview.onDidReceiveMessage(data => {
            switch (data.type) {
                case 'sendMessage':
                    vscode.window.showInformationMessage(`You said: ${data.message}`);
                    break;
            }
        });
    }

    public show() {
        if (this._view) {
            this._view.show?.(true);
        }
    }

    private _getHtmlForWebview(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeMind AI Chat</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            padding: 20px;
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
        }
        input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            background: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>🤖 CodeMind AI Chat</h1>
    <p>Welcome! This is a working chat interface.</p>
    <input type="text" id="messageInput" placeholder="Type your message here..." />
    <button onclick="sendMessage()">Send Message</button>

    <script>
        const vscode = acquireVsCodeApi();

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (message) {
                vscode.postMessage({
                    type: 'sendMessage',
                    message: message
                });
                input.value = '';
            }
        }

        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>`;
    }
}

let chatWebviewProvider: ChatWebviewProvider;

export function activate(context: vscode.ExtensionContext) {
    console.log('🚀 CodeMind AI Assistant is now active!');
    console.log('🔧 Extension URI:', context.extensionUri.toString());

    try {
        // Initialize and register webview provider IMMEDIATELY
        console.log('📦 Creating ChatWebviewProvider...');
        chatWebviewProvider = new ChatWebviewProvider(context.extensionUri);
        console.log('✅ ChatWebviewProvider created');

        // Register webview provider
        console.log('📝 Registering webview provider with ID: codemind-ai.chatView');
        const webviewProvider = vscode.window.registerWebviewViewProvider(
            'codemind-ai.chatView',
            chatWebviewProvider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        );
        context.subscriptions.push(webviewProvider);
        console.log('✅ Webview provider registered and added to subscriptions');

        // Register commands
        console.log('📝 Registering commands...');
    const openChatCommand = vscode.commands.registerCommand('codemind-ai.openChat', () => {
        console.log('🎯 Open Chat command executed!');
        chatWebviewProvider.show();
    });





        // Add commands to subscriptions
        context.subscriptions.push(openChatCommand);

        // Show welcome message on first activation
        const hasShownWelcome = context.globalState.get('codemind-ai.hasShownWelcome', false);
        if (!hasShownWelcome) {
            vscode.window.showInformationMessage(
                'Welcome to CodeMind AI Assistant! Look for "CodeMind AI Chat" in the Explorer sidebar.',
                'Got it!'
            );
            context.globalState.update('codemind-ai.hasShownWelcome', true);
        }

        console.log('🎉 Extension activation completed successfully!');



    } catch (error) {
        console.error('❌ Extension activation failed:', error);
        vscode.window.showErrorMessage(`CodeMind AI Assistant failed to activate: ${error instanceof Error ? error.message : 'Unknown error'}`);

        // Register a minimal command so the extension doesn't completely fail
        const fallbackCommand = vscode.commands.registerCommand('codemind-ai.openChat', () => {
            vscode.window.showErrorMessage('CodeMind AI Assistant is not properly initialized. Please check the console for errors.');
        });
        context.subscriptions.push(fallbackCommand);
    }
}

export function deactivate() {
    console.log('CodeMind AI Assistant is now deactivated');
}
