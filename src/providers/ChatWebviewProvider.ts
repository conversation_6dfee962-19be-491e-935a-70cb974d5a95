import * as vscode from 'vscode';
import { AugmentAIProvider, ChatMessage } from './AugmentAIProvider';
import { CodeContextManager } from '../managers/CodeContextManager';
import { marked } from 'marked';

export class ChatWebviewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'codemind-ai.chatView';
    private _view?: vscode.WebviewView;
    private _context: vscode.ExtensionContext;
    private _aiProvider: AugmentAIProvider;
    private _codeContextManager: CodeContextManager;

    constructor(
        context: vscode.ExtensionContext,
        aiProvider: AugmentAIProvider,
        codeContextManager: CodeContextManager
    ) {
        this._context = context;
        this._aiProvider = aiProvider;
        this._codeContextManager = codeContextManager;
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        console.log('🎉 ChatWebviewProvider.resolveWebviewView called!');
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._context.extensionUri
            ]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'sendMessage':
                    await this.handleUserMessage(data.message);
                    break;
                case 'clearChat':
                    this.clearChat();
                    break;
                case 'insertCode':
                    this.insertCodeIntoEditor(data.code);
                    break;
                case 'copyCode':
                    vscode.env.clipboard.writeText(data.code);
                    vscode.window.showInformationMessage('Code copied to clipboard');
                    break;
            }
        });

        // Load conversation history
        this.loadConversationHistory();
    }

    private async handleUserMessage(message: string): Promise<void> {
        if (!message.trim()) {
            return;
        }

        // Show user message immediately
        this.addMessageToChat('user', message);

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Get workspace context
            const workspaceContext = await this._codeContextManager.getWorkspaceContext();
            
            // Send message to AI
            const response = await this._aiProvider.sendMessage(message, workspaceContext);
            
            // Hide typing indicator and show response
            this.hideTypingIndicator();
            this.addMessageToChat('assistant', response.content);

            // Show token usage if available
            if (response.usage) {
                this.showTokenUsage(response.usage);
            }

        } catch (error) {
            this.hideTypingIndicator();
            const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
            this.addMessageToChat('error', `Error: ${errorMessage}`);
            
            if (errorMessage.includes('API key')) {
                this.showConfigurationPrompt();
            }
        }
    }

    private addMessageToChat(role: 'user' | 'assistant' | 'error', content: string): void {
        if (!this._view) {
            return;
        }

        let processedContent = content;
        
        if (role === 'assistant') {
            // Convert markdown to HTML for assistant messages
            processedContent = marked(content) as string;
        }

        this._view.webview.postMessage({
            type: 'addMessage',
            role,
            content: processedContent,
            timestamp: Date.now()
        });
    }

    private showTypingIndicator(): void {
        if (!this._view) {
            return;
        }

        this._view.webview.postMessage({
            type: 'showTyping'
        });
    }

    private hideTypingIndicator(): void {
        if (!this._view) {
            return;
        }

        this._view.webview.postMessage({
            type: 'hideTyping'
        });
    }

    private showTokenUsage(usage: { promptTokens: number; completionTokens: number; totalTokens: number }): void {
        if (!this._view) {
            return;
        }

        this._view.webview.postMessage({
            type: 'showTokenUsage',
            usage
        });
    }

    private showConfigurationPrompt(): void {
        if (!this._view) {
            return;
        }

        this._view.webview.postMessage({
            type: 'showConfigPrompt'
        });
    }

    private clearChat(): void {
        this._aiProvider.clearConversation();
        
        if (!this._view) {
            return;
        }

        this._view.webview.postMessage({
            type: 'clearChat'
        });
    }

    private insertCodeIntoEditor(code: string): void {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }

        const selection = editor.selection;
        editor.edit(editBuilder => {
            editBuilder.replace(selection, code);
        });
    }

    private loadConversationHistory(): void {
        const history = this._aiProvider.getConversationHistory();
        
        // Skip system message and load user/assistant messages
        for (let i = 1; i < history.length; i++) {
            const message = history[i];
            if (message.role !== 'system') {
                this.addMessageToChat(message.role as 'user' | 'assistant', message.content);
            }
        }
    }

    public sendMessage(message: string): void {
        if (!this._view) {
            return;
        }

        this._view.webview.postMessage({
            type: 'setInputValue',
            value: message
        });

        // Trigger send
        this.handleUserMessage(message);
    }

    public show(): void {
        console.log('🔍 ChatWebviewProvider.show() called, _view exists:', !!this._view);

        if (this._view) {
            console.log('📱 Showing existing webview');
            this._view.show?.(true);
        } else {
            console.log('🚀 Webview not resolved yet, revealing view');
            // If webview isn't resolved yet, reveal the view container
            Promise.resolve(vscode.commands.executeCommand('workbench.view.explorer')).then(() => {
                console.log('📂 Explorer revealed');
                return vscode.commands.executeCommand('codemind-ai.chatView.focus');
            }).then(() => {
                console.log('🎯 Chat view focused');
            }).catch((error: any) => {
                console.error('❌ Error showing chat view:', error);
                vscode.window.showErrorMessage('Failed to show chat view: ' + (error?.message || 'Unknown error'));
            });
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeMind AI Chat</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .message {
            padding: 8px 12px;
            border-radius: 8px;
            max-width: 90%;
            word-wrap: break-word;
        }
        
        .message.user {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            align-self: flex-end;
        }
        
        .message.assistant {
            background-color: var(--vscode-editor-selectionBackground);
            align-self: flex-start;
        }
        
        .message.error {
            background-color: var(--vscode-errorBackground);
            color: var(--vscode-errorForeground);
            align-self: flex-start;
        }
        
        .typing-indicator {
            display: none;
            padding: 8px 12px;
            background-color: var(--vscode-editor-selectionBackground);
            border-radius: 8px;
            align-self: flex-start;
            font-style: italic;
            opacity: 0.7;
        }
        
        .input-container {
            padding: 10px;
            border-top: 1px solid var(--vscode-panel-border);
            display: flex;
            gap: 8px;
        }
        
        .input-box {
            flex: 1;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-family: inherit;
            font-size: inherit;
            resize: vertical;
            min-height: 20px;
            max-height: 100px;
        }
        
        .send-button, .clear-button {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            cursor: pointer;
            font-family: inherit;
        }
        
        .send-button:hover, .clear-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .clear-button {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        
        .code-block {
            position: relative;
            margin: 8px 0;
        }
        
        .code-actions {
            position: absolute;
            top: 5px;
            right: 5px;
            display: flex;
            gap: 5px;
        }
        
        .code-action-btn {
            padding: 2px 6px;
            font-size: 11px;
            border: none;
            border-radius: 3px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            cursor: pointer;
        }
        
        .token-usage {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            text-align: right;
            margin-top: 5px;
        }
        
        pre {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 12px;
            border-radius: 4px;
            overflow-x: auto;
        }
        
        code {
            font-family: var(--vscode-editor-font-family);
        }
    </style>
</head>
<body>
    <div class="chat-container" id="chatContainer">
        <div class="message assistant">
            Welcome to CodeMind AI Assistant! I'm here to help you with coding tasks, explanations, and more. How can I assist you today?
        </div>
    </div>
    
    <div class="typing-indicator" id="typingIndicator">
        AI is thinking...
    </div>
    
    <div class="input-container">
        <textarea class="input-box" id="messageInput" placeholder="Ask me anything about your code..." rows="1"></textarea>
        <button class="send-button" id="sendButton">Send</button>
        <button class="clear-button" id="clearButton">Clear</button>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const clearButton = document.getElementById('clearButton');
        const typingIndicator = document.getElementById('typingIndicator');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });

        // Send message on Enter (but allow Shift+Enter for new lines)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendButton.addEventListener('click', sendMessage);
        clearButton.addEventListener('click', clearChat);

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            vscode.postMessage({
                type: 'sendMessage',
                message: message
            });

            messageInput.value = '';
            messageInput.style.height = 'auto';
        }

        function clearChat() {
            vscode.postMessage({
                type: 'clearChat'
            });
        }

        function addMessage(role, content, timestamp) {
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${role}\`;
            
            if (role === 'assistant') {
                messageDiv.innerHTML = content;
                
                // Add code action buttons to code blocks
                const codeBlocks = messageDiv.querySelectorAll('pre code');
                codeBlocks.forEach(codeBlock => {
                    const pre = codeBlock.parentElement;
                    pre.style.position = 'relative';
                    
                    const actionsDiv = document.createElement('div');
                    actionsDiv.className = 'code-actions';
                    
                    const copyBtn = document.createElement('button');
                    copyBtn.className = 'code-action-btn';
                    copyBtn.textContent = 'Copy';
                    copyBtn.onclick = () => {
                        vscode.postMessage({
                            type: 'copyCode',
                            code: codeBlock.textContent
                        });
                    };
                    
                    const insertBtn = document.createElement('button');
                    insertBtn.className = 'code-action-btn';
                    insertBtn.textContent = 'Insert';
                    insertBtn.onclick = () => {
                        vscode.postMessage({
                            type: 'insertCode',
                            code: codeBlock.textContent
                        });
                    };
                    
                    actionsDiv.appendChild(copyBtn);
                    actionsDiv.appendChild(insertBtn);
                    pre.appendChild(actionsDiv);
                });
            } else {
                messageDiv.textContent = content;
            }
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.type) {
                case 'addMessage':
                    addMessage(message.role, message.content, message.timestamp);
                    break;
                case 'showTyping':
                    typingIndicator.style.display = 'block';
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                    break;
                case 'hideTyping':
                    typingIndicator.style.display = 'none';
                    break;
                case 'clearChat':
                    chatContainer.innerHTML = '<div class="message assistant">Chat cleared. How can I help you?</div>';
                    break;
                case 'setInputValue':
                    messageInput.value = message.value;
                    break;
                case 'showTokenUsage':
                    const usageDiv = document.createElement('div');
                    usageDiv.className = 'token-usage';
                    usageDiv.textContent = \`Tokens: \${message.usage.totalTokens} (prompt: \${message.usage.promptTokens}, completion: \${message.usage.completionTokens})\`;
                    chatContainer.appendChild(usageDiv);
                    break;
                case 'showConfigPrompt':
                    const configDiv = document.createElement('div');
                    configDiv.className = 'message error';
                    configDiv.innerHTML = 'Please configure your API key in VS Code settings: <strong>Augment AI > API Key</strong>';
                    chatContainer.appendChild(configDiv);
                    break;
            }
        });
    </script>
</body>
</html>`;
    }
}
