import * as vscode from 'vscode';

export interface AugmentAIConfig {
    apiProvider: 'openai' | 'anthropic';
    apiKey: string;
    model: string;
    maxTokens: number;
    temperature: number;
}

export class ConfigurationManager {
    private config: AugmentAIConfig;

    constructor() {
        this.config = this.loadConfiguration();
    }

    private loadConfiguration(): AugmentAIConfig {
        const config = vscode.workspace.getConfiguration('codemind-ai');
        
        return {
            apiProvider: config.get('apiProvider', 'anthropic'),
            apiKey: config.get('apiKey', ''),
            model: config.get('model', 'claude-3-sonnet-20240229'),
            maxTokens: config.get('maxTokens', 4000),
            temperature: config.get('temperature', 0.1)
        };
    }

    public getConfiguration(): AugmentAIConfig {
        return { ...this.config };
    }

    public refresh(): void {
        this.config = this.loadConfiguration();
    }

    public isConfigured(): boolean {
        return this.config.apiKey.length > 0;
    }

    public async promptForApiKey(): Promise<boolean> {
        const apiKey = await vscode.window.showInputBox({
            prompt: `Enter your ${this.config.apiProvider.toUpperCase()} API key`,
            password: true,
            placeHolder: 'sk-...'
        });

        if (apiKey) {
            await vscode.workspace.getConfiguration('codemind-ai').update(
                'apiKey',
                apiKey,
                vscode.ConfigurationTarget.Global
            );
            this.refresh();
            return true;
        }

        return false;
    }

    public getModelEndpoint(): string {
        switch (this.config.apiProvider) {
            case 'openai':
                return 'https://api.openai.com/v1/chat/completions';
            case 'anthropic':
                return 'https://api.anthropic.com/v1/messages';
            default:
                throw new Error(`Unsupported API provider: ${this.config.apiProvider}`);
        }
    }

    public getHeaders(): Record<string, string> {
        const headers: Record<string, string> = {
            'Content-Type': 'application/json'
        };

        switch (this.config.apiProvider) {
            case 'openai':
                headers['Authorization'] = `Bearer ${this.config.apiKey}`;
                break;
            case 'anthropic':
                headers['x-api-key'] = this.config.apiKey;
                headers['anthropic-version'] = '2023-06-01';
                break;
        }

        return headers;
    }

    public formatRequestBody(messages: Array<{role: string, content: string}>): any {
        switch (this.config.apiProvider) {
            case 'openai':
                return {
                    model: this.config.model,
                    messages: messages,
                    max_tokens: this.config.maxTokens,
                    temperature: this.config.temperature
                };
            case 'anthropic':
                // Convert messages to Anthropic format
                const systemMessage = messages.find(m => m.role === 'system');
                const userMessages = messages.filter(m => m.role !== 'system');
                
                return {
                    model: this.config.model,
                    max_tokens: this.config.maxTokens,
                    temperature: this.config.temperature,
                    system: systemMessage?.content || '',
                    messages: userMessages
                };
            default:
                throw new Error(`Unsupported API provider: ${this.config.apiProvider}`);
        }
    }

    public extractResponse(response: any): string {
        switch (this.config.apiProvider) {
            case 'openai':
                return response.choices?.[0]?.message?.content || 'No response received';
            case 'anthropic':
                return response.content?.[0]?.text || 'No response received';
            default:
                throw new Error(`Unsupported API provider: ${this.config.apiProvider}`);
        }
    }
}
