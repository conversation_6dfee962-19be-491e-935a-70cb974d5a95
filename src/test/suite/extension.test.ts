import * as assert from 'assert';
import * as vscode from 'vscode';
import { ConfigurationManager } from '../../managers/ConfigurationManager';
import { CodeContextManager } from '../../managers/CodeContextManager';
import { FileOperationsManager } from '../../managers/FileOperationsManager';

suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    test('Configuration Manager', () => {
        const configManager = new ConfigurationManager();
        const config = configManager.getConfiguration();
        
        assert.ok(config);
        assert.ok(typeof config.apiProvider === 'string');
        assert.ok(typeof config.maxTokens === 'number');
        assert.ok(typeof config.temperature === 'number');
    });

    test('Configuration Manager - API Provider Validation', () => {
        const configManager = new ConfigurationManager();
        const config = configManager.getConfiguration();
        
        assert.ok(['openai', 'anthropic'].includes(config.apiProvider));
    });

    test('Configuration Manager - Model Endpoint', () => {
        const configManager = new ConfigurationManager();
        const endpoint = configManager.getModelEndpoint();
        
        assert.ok(endpoint.startsWith('https://'));
        assert.ok(endpoint.includes('api'));
    });

    test('Code Context Manager', () => {
        const contextManager = new CodeContextManager();
        assert.ok(contextManager);
    });

    test('File Operations Manager', () => {
        const fileOpsManager = new FileOperationsManager();
        assert.ok(fileOpsManager);
    });

    test('File Operations Manager - List Files', async () => {
        const fileOpsManager = new FileOperationsManager();
        const files = await fileOpsManager.listFiles();
        
        assert.ok(Array.isArray(files));
    });

    test('Configuration Manager - Headers Generation', () => {
        const configManager = new ConfigurationManager();
        const headers = configManager.getHeaders();
        
        assert.ok(headers);
        assert.ok(headers['Content-Type'] === 'application/json');
        
        const config = configManager.getConfiguration();
        if (config.apiProvider === 'openai') {
            assert.ok('Authorization' in headers);
        } else if (config.apiProvider === 'anthropic') {
            assert.ok('x-api-key' in headers);
            assert.ok('anthropic-version' in headers);
        }
    });

    test('Configuration Manager - Request Body Formatting', () => {
        const configManager = new ConfigurationManager();
        const messages = [
            { role: 'user', content: 'Hello' }
        ];
        
        const requestBody = configManager.formatRequestBody(messages);
        
        assert.ok(requestBody);
        assert.ok(typeof requestBody === 'object');
        assert.ok('model' in requestBody);
        assert.ok('max_tokens' in requestBody || 'maxTokens' in requestBody);
    });

    test('Configuration Manager - Response Extraction', () => {
        const configManager = new ConfigurationManager();
        const config = configManager.getConfiguration();
        
        let mockResponse: any;
        
        if (config.apiProvider === 'openai') {
            mockResponse = {
                choices: [{
                    message: {
                        content: 'Test response'
                    }
                }]
            };
        } else {
            mockResponse = {
                content: [{
                    text: 'Test response'
                }]
            };
        }
        
        const extractedResponse = configManager.extractResponse(mockResponse);
        assert.strictEqual(extractedResponse, 'Test response');
    });

    test('Extension Commands Registration', async () => {
        // Test that our commands are registered
        const commands = await vscode.commands.getCommands();
        
        assert.ok(commands.includes('augment-ai.openChat'));
        assert.ok(commands.includes('augment-ai.explainCode'));
        assert.ok(commands.includes('augment-ai.generateCode'));
        assert.ok(commands.includes('augment-ai.refactorCode'));
    });
});
